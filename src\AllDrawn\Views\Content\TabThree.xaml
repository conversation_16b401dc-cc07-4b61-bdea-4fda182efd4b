﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.TabButtons"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    xmlns:views1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views"
    x:DataType="viewModels:MainPageViewModel"
    HorizontalOptions="Fill"
    Tag="ThisTab"
    VerticalOptions="Fill">


    <draw:SkiaScroll
        BackgroundColor="Blue"
        HorizontalOptions="Fill"
        Tag="TabScroll"
        VerticalOptions="Fill">

        <draw:SkiaControl.FillGradient>

            <draw:SkiaGradient
                EndXRatio="1"
                EndYRatio="1"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#889955</Color>
                    <Color>#886655</Color>
                    <Color>#222222</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

        </draw:SkiaControl.FillGradient>

        <draw:SkiaLayout
            Padding="0,32,0,0"
            x:DataType="viewModels:MainPageViewModel"
            HorizontalOptions="Fill"
            Spacing="20"
            Tag="TabSettings"
            Type="Column"
            UseCache="Operations">

            <!--  header  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Spacing="16"
                Type="Column"
                UseCache="Image">

                <draw:SkiaLabel
                    DropShadowColor="#33333333"
                    DropShadowSize="4"
                    FontFamily="FontTextTitle"
                    FontSize="44"
                    HorizontalOptions="Center"
                    StrokeColor="White"
                    StrokeWidth="2"
                    Text="{Binding Title}"
                    UseCache="None">
                    <draw:SkiaLabel.FillGradient>

                        <draw:SkiaGradient
                            EndXRatio="0"
                            EndYRatio="1"
                            StartXRatio="0"
                            StartYRatio="0"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>White</Color>
                                <Color>Yellow</Color>
                                <Color>Orange</Color>
                                <Color>Red</Color>
                                <Color>DarkRed</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaLabel.FillGradient>
                </draw:SkiaLabel>

                <draw:SkiaLabel
                    Margin="32,0,32,8"
                    HorizontalOptions="Center"
                    Style="{x:StaticResource SkiaLabelDefaultStyle}"
                    Text="The centered SkiaLayout Stack below will auto-position buttons in a flex-like manner because Split property is set to zero. When set to 1 that would be 1 column and so on.."
                    UseCache="None" />

            </draw:SkiaLayout>

            <!--  BUTTONS  -->
            <draw:SkiaLayout
                Margin="16,16,16,0"
                HorizontalOptions="Center"
                Spacing="16"
                Split="0"
                Tag="ButtonsStack"
                Type="Wrap"
                UseCache="ImageComposite">

                <!--<controls1:SmallButton
                CommandTapped="{Binding CommandPushPage}"
                HorizontalOptions="Center"
                Text="Simple View" />-->

                <!--<controls1:SmallButton
                        CommandTapped="{Binding CommandPushLabels}"
                        HorizontalOptions="Center"
                        Text="Some Text" />-->

                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushControls}"
                    Text="Controls" />


                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushLottieAndFriends}"
                    Text="Lottie and other" />

                <controls1:SmallButton
                    Tag="33"
                    CommandTapped="{Binding CommandPushTransforms}"
                    Text="Transforms" />

                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushVarious}"
                    Text="Maui Element" />

                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushDrawer}"
                    Text="Drawer" />

                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushCarousel}"
                    Text="Carousel" />


                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushMaui}"
                    Text="SkiaShell Modal" />

                <controls1:SmallButton
                    CommandTapped="{Binding CommandOpenToast}"
                    Text="SkiaShell Toast" />

                <!--<controls1:SmallButton
                    CommandTapped="{Binding CommandPushPdf}"
                    Text="XAML to PDF" />-->

                <controls1:SmallButton
                    CommandTapped="{Binding CommandCamera}"
                    Text="Drawn Camera">
                    <!--<draw:SkiaControl.IsVisible>
                        <OnPlatform x:TypeArguments="x:Boolean">
                            <On
                                Platform="Android"
                                Value="True" />
                        </OnPlatform>
                    </draw:SkiaControl.IsVisible>-->
                </controls1:SmallButton>

                <controls1:SmallButton
                    CommandTapped="{Binding CommandPushFun}"
                    Text="Some Fun!" />

                <!--<controls1:SmallButton
                CommandTapped="{Binding CommandDebugAction}"
                HorizontalOptions="Center"
                Text="Debug" />-->

            </draw:SkiaLayout>

            <draw:SkiaLabel
                Margin="32,32,32,8"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Style="{x:StaticResource SkiaLabelDefaultStyle}"
                Text="For more demo cases please check out the original DrawnUI repo Sandbox project!"
                TextColor="BurlyWood"
                UseCache="Operations" />

            <!--  content padding for bottom tabs  -->
            <draw:SkiaControl
                BackgroundColor="Transparent"
                HeightRequest="{Binding Presentation.BottomTabsHeightRequest}"
                WidthRequest="1" />

        </draw:SkiaLayout>

    </draw:SkiaScroll>

    <draw:SkiaShape
        Margin="16,0,0,80"
        BackgroundColor="#66000000"
        CornerRadius="20"
        IsClippedToBounds="True"
        IsVisible="{Binding ShowPhoto}"
        LockRatio="1"
        StrokeColor="White"
        StrokeWidth="1"
        UseCache="GPU"
        VerticalOptions="End"
        WidthRequest="120">

        <draw:SkiaImage
            x:Name="ImagePreview"
            Aspect="AspectCover"
            HorizontalOptions="Fill"
            ImageBitmap="{Binding DisplayPreview}"
            Tag="Preview"
            VerticalOptions="Fill" />

    </draw:SkiaShape>

    <draw:SkiaDrawer
        x:Name="TabDrawer"
        Bounces="False"
        Direction="FromBottom"
        HeightRequest="550"
        IsOpen="{Binding DrawerOpen}"
        Tag="Drawer"
        UseCache="Operations"
        VerticalOptions="End"
        ZIndex="10">

        <views1:ModalContent />

    </draw:SkiaDrawer>


</draw:SkiaLayout>
