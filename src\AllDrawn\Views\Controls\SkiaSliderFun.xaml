<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaSlider
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.Controls.SkiaSliderFun"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisSlider"
    AvailableWidthAdjustment="2"
    HorizontalOptions="Fill"
    Tag="SliderFun"
    Type="Column"
    UseCache="None">

    <draw:SkiaSlider.Resources>
        <ResourceDictionary />
    </draw:SkiaSlider.Resources>

    <!--  MAIN GRID  -->
    <draw:SkiaLayout
        x:Name="ThisSliderGrid"
        Margin="0,0,0,0"
        HeightRequest="{Binding Source={x:Reference ThisSlider}, Path=SliderHeight}"
        HorizontalOptions="Fill"
        Tag="Trail"
        VerticalOptions="Start">

        <!--  TRAIL  -->
        <draw:SkiaShape
            BackgroundColor="{StaticResource ColorPrimaryDark}"
            CornerRadius="4.5"
            HeightRequest="9"
            HorizontalOptions="Fill"
            UseCache="Operations"
            VerticalOptions="Center" />

        <!--  SELECTED TRAIL  -->
        <draw:SliderTrail
            BackgroundColor="{StaticResource ColorPrimary}"
            CornerRadius="9"
            HeightRequest="16"
            HorizontalOptions="Start"
            ModifyXPosEnd="20"
            SideOffset="0"
            StrokeBlendMode="Color"
            StrokeColor="{StaticResource ColorPrimaryDark}"
            StrokeWidth="2"
            Tag="SelectedTrail"
            VerticalOptions="Center"
            XPos="{Binding Source={x:Reference StartThumb}, Path=TranslationX}"
            XPosEnd="{Binding Source={x:Reference EndThumb}, Path=TranslationX}" />

        <!--  START THUMB - RANGED ONLY  -->
        <draw:SliderThumb
            x:Name="StartThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            IsVisible="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=StartThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="4"
                BackgroundColor="{StaticResource ColorPrimary}"
                HorizontalOptions="Fill"
                StrokeColor="{StaticResource ColorPrimaryDark}"
                StrokeWidth="1"
                Type="Circle"
                VerticalOptions="Fill">
                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="{StaticResource ColorPrimaryDark}" />

                </draw:SkiaShape.Shadows>

                <draw:SkiaShape
                    BackgroundColor="White"
                    HorizontalOptions="Center"
                    LockRatio="1"
                    Type="Circle"
                    VerticalOptions="Center"
                    WidthRequest="8" />

            </draw:SkiaShape>


        </draw:SliderThumb>

        <!--  END THUMB  -->
        <draw:SliderThumb
            x:Name="EndThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=EndThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="4"
                BackgroundColor="{StaticResource ColorPrimary}"
                HorizontalOptions="Fill"
                StrokeColor="{StaticResource ColorPrimaryDark}"
                StrokeWidth="1"
                Type="Circle"
                VerticalOptions="Fill">
                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="{StaticResource ColorPrimaryDark}" />

                </draw:SkiaShape.Shadows>

                <draw:SkiaShape
                    BackgroundColor="White"
                    HorizontalOptions="Center"
                    LockRatio="1"
                    Type="Circle"
                    VerticalOptions="Center"
                    WidthRequest="8" />

            </draw:SkiaShape>

        </draw:SliderThumb>

    </draw:SkiaLayout>

    <!--  BOTTOM INFO  -->
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        UseCache="Operations"
        VerticalOptions="Start">

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="Start"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MinDesc}"
            TextColor="Black" />

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="End"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MaxDesc}"
            TextColor="Black" />

    </draw:SkiaLayout>

</draw:SkiaSlider>
