<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaSlider
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.Controls.SkiaSliderTech"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisSlider"
    HorizontalOptions="Fill"
    Type="Column"
    UseCache="None">

    <draw:SkiaSlider.Resources>
        <ResourceDictionary>

            <x:String x:Key="SvgThumbIcon">
                <![CDATA[

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M182.6 9.4c-12.5-12.5-32.8-12.5-45.3 0l-96 96c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L128 109.3V402.7L86.6 361.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l96 96c12.5 12.5 32.8 12.5 45.3 0l96-96c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 402.7V109.3l41.4 41.4c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-96-96z"/></svg>

                ]]>

            </x:String>



        </ResourceDictionary>
    </draw:SkiaSlider.Resources>




    <!--  MAIN GRID  -->
    <draw:SkiaLayout
        x:Name="ThisSliderGrid"
        Margin="0,8,0,0"
        HeightRequest="{Binding Source={x:Reference ThisSlider}, Path=SliderHeight}"
        HorizontalOptions="Fill"
        Tag="Trail"
        VerticalOptions="Start">

        <!--  TRAIL  -->
        <draw:SkiaShape
            BackgroundColor="Gray"
            CornerRadius="4"
            HeightRequest="6"
            HorizontalOptions="Fill"
            UseCache="Operations"
            VerticalOptions="Center">
            <draw:SkiaControl.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="1"
                    StartXRatio="1"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#A0A0A0</Color>
                        <Color>#E6E6E6</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaControl.FillGradient>
        </draw:SkiaShape>

        <!--  SELECTED TRAIL  -->
        <draw:SliderTrail
            BackgroundColor="{StaticResource ColorPrimary}"
            CornerRadius="5"
            HeightRequest="8"
            HorizontalOptions="Start"
            ModifyXPosEnd="5"
            SideOffset="0"
            VerticalOptions="Center"
            XPos="{Binding Source={x:Reference StartThumb}, Path=TranslationX}"
            XPosEnd="{Binding Source={x:Reference EndThumb}, Path=TranslationX}">
            <draw:SkiaControl.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="0"
                    StartXRatio="1"
                    StartYRatio="1"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#99889955</Color>
                        <Color>#889955</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaControl.FillGradient>


        </draw:SliderTrail>

        <!--  START THUMB - RANGED ONLY  -->
        <draw:SliderThumb
            x:Name="StartThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            IsVisible="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=StartThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                BackgroundColor="{StaticResource ColorPrimary}"
                CornerRadius="8"
                HorizontalOptions="Fill"
                StrokeColor="Black"
                StrokeWidth="4"
                Type="Rectangle"
                UseCache="Image"
                VerticalOptions="Fill">
                <!--<draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="Black" />

                </draw:SkiaShape.Shadows>-->

                <draw:SkiaShape.StrokeGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="1"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#E6E6E6</Color>
                            <Color>#A0A0A0</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.StrokeGradient>

                <draw:SkiaControl.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="1"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#A0A0A0</Color>
                            <Color>#E6E6E6</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaControl.FillGradient>



            </draw:SkiaShape>

            <draw:SkiaSvg
                Margin="10"
                HorizontalOptions="Fill"
                Rotation="90"
                SvgString="{StaticResource SvgThumbIcon}"
                TintColor="Gray"
                UseCache="Operations"
                VerticalOptions="Fill">
                <draw:SkiaControl.Triggers>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisSlider}, Path=IsPressed}"
                        TargetType="draw:SkiaSvg"
                        Value="True">
                        <Setter Property="TintColor" Value="Red" />
                    </DataTrigger>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisSlider}, Path=IsPressed}"
                        TargetType="draw:SkiaSvg"
                        Value="False">
                        <Setter Property="TintColor" Value="Gray" />
                    </DataTrigger>
                </draw:SkiaControl.Triggers>

            </draw:SkiaSvg>

        </draw:SliderThumb>

        <!--  END THUMB  -->
        <draw:SliderThumb
            x:Name="EndThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=EndThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                BackgroundColor="{StaticResource ColorPrimary}"
                CornerRadius="8"
                HorizontalOptions="Fill"
                StrokeColor="Black"
                StrokeWidth="4"
                Type="Rectangle"
                UseCache="Image"
                VerticalOptions="Fill">
                <!--<draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="Black" />

                </draw:SkiaShape.Shadows>-->

                <draw:SkiaShape.StrokeGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="1"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#E6E6E6</Color>
                            <Color>#A0A0A0</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.StrokeGradient>

                <draw:SkiaControl.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="1"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#A0A0A0</Color>
                            <Color>#E6E6E6</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaControl.FillGradient>



            </draw:SkiaShape>

            <draw:SkiaSvg
                Margin="10"
                HorizontalOptions="Fill"
                Rotation="90"
                SvgString="{StaticResource SvgThumbIcon}"
                TintColor="Gray"
                UseCache="Operations"
                VerticalOptions="Fill">
                <draw:SkiaControl.Triggers>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisSlider}, Path=IsPressed}"
                        TargetType="draw:SkiaSvg"
                        Value="True">
                        <Setter Property="TintColor" Value="Red" />
                    </DataTrigger>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisSlider}, Path=IsPressed}"
                        TargetType="draw:SkiaSvg"
                        Value="False">
                        <Setter Property="TintColor" Value="Gray" />
                    </DataTrigger>
                </draw:SkiaControl.Triggers>

            </draw:SkiaSvg>

        </draw:SliderThumb>

    </draw:SkiaLayout>

    <!--  BOTTOM INFO  -->
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        UseCache="Operations"
        VerticalOptions="Start">

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="Start"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MinDesc}"
            TextColor="Black" />

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="End"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MaxDesc}"
            TextColor="Black" />

    </draw:SkiaLayout>

</draw:SkiaSlider>
