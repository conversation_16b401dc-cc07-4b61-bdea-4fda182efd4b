<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.MainPageCamera"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="clr-namespace:ShadersCamera.ViewModels"
    xmlns:controls="clr-namespace:ShadersCamera.Controls"
    xmlns:models="clr-namespace:ShadersCamera.Models"
    x:Name="ThisPage"
    x:DataType="viewModels:CameraViewModel">

    <draw:Canvas
        ViewDisposing="CanvasWillDispose"
        WillFirstTimeDraw="DrawnView_OnWillFirstTimeDraw"
        Gestures="Enabled"
        HorizontalOptions="Fill" VerticalOptions="Fill"
        RenderingMode="Accelerated"
        Tag="MainPage">

        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaLayer
                Tapped="TappedBackground"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <draw:SkiaLayer.Resources>
                    <ResourceDictionary>

                        <x:String x:Key="SvgShutter">
                            <![CDATA[                                      
                <svg fill="#000000" width="800px" height="800px" viewBox="0 0 16 16" id="photo-camera-16px" xmlns="http://www.w3.org/2000/svg">
                  <path id="Path_62" data-name="Path 62" d="M-9.5,4h-1.154L-11.789.973A1.506,1.506,0,0,0-13.193,0h-3.614a1.506,1.506,0,0,0-1.4.973L-19.346,4H-20.5A2.5,2.5,0,0,0-23,6.5v7A2.5,2.5,0,0,0-20.5,16h11A2.5,2.5,0,0,0-7,13.5v-7A2.5,2.5,0,0,0-9.5,4Zm-7.775-2.675A.5.5,0,0,1-16.807,1h3.614a.5.5,0,0,1,.468.325l1,2.675h-6.556ZM-8,13.5A1.5,1.5,0,0,1-9.5,15h-11A1.5,1.5,0,0,1-22,13.5v-7A1.5,1.5,0,0,1-20.5,5h11A1.5,1.5,0,0,1-8,6.5ZM-15,6a4,4,0,0,0-4,4,4,4,0,0,0,4,4,4,4,0,0,0,4-4A4,4,0,0,0-15,6Zm0,7a3,3,0,0,1-3-3,3,3,0,0,1,3-3,3,3,0,0,1,3,3A3,3,0,0,1-15,13Zm.5-4.5A.5.5,0,0,1-15,9a1,1,0,0,0-1,1,.5.5,0,0,1-.5.5A.5.5,0,0,1-17,10a2,2,0,0,1,2-2A.5.5,0,0,1-14.5,8.5Z" transform="translate(23)"/>
                </svg>                                
                ]]>
                        </x:String>

                        <x:String x:Key="SvgClose">
                            <![CDATA[ 
                                     
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.88173 5.9864L11.8557 1.0257C11.954 0.911231 12.0054 0.76398 11.9996 0.613378C11.9937 0.462776 11.9311 0.319916 11.8243 0.213345C11.7174 0.106774 11.5742 0.0443419 11.4232 0.0385248C11.2722 0.0327077 11.1245 0.0839342 11.0097 0.181967L6.03573 5.14266L1.06173 0.175983C0.948744 0.063303 0.795507 0 0.635726 0C0.475945 0 0.322708 0.063303 0.209726 0.175983C0.0967439 0.288663 0.0332711 0.44149 0.0332711 0.600844C0.0332711 0.760197 0.0967439 0.913024 0.209726 1.0257L5.18973 5.9864L0.209726 10.9471C0.146917 11.0007 0.0959048 11.0668 0.0598909 11.141C0.0238769 11.2152 0.00363881 11.2961 0.000447115 11.3785C-0.00274458 11.4609 0.0111787 11.5431 0.0413434 11.6199C0.0715082 11.6967 0.117263 11.7664 0.175736 11.8247C0.234209 11.8831 0.304137 11.9287 0.381132 11.9588C0.458127 11.9889 0.540527 12.0027 0.623158 11.9996C0.70579 11.9964 0.786869 11.9762 0.861308 11.9403C0.935747 11.9044 1.00194 11.8535 1.05573 11.7908L6.03573 6.83014L11.0097 11.7908C11.1245 11.8889 11.2722 11.9401 11.4232 11.9343C11.5742 11.9285 11.7174 11.866 11.8243 11.7595C11.9311 11.6529 11.9937 11.51 11.9996 11.3594C12.0054 11.2088 11.954 11.0616 11.8557 10.9471L6.88173 5.9864Z" fill="white"/>
                    </svg>
                                
                ]]>

                        </x:String>

                    </ResourceDictionary>
                </draw:SkiaLayer.Resources>

                <!--  CAMERA PREVIEW ETC  -->
                <controls:CameraWithEffects
                    ConstantUpdate="True"
                    x:Name="CameraControl"
                    BackgroundColor="Black"
                    CapturePhotoQuality="Medium"
                    Facing="Default"
                    HorizontalOptions="Fill"
                    Tag="Camera"
                    VerticalOptions="Fill"
                    ZIndex="-1"
                    ZoomLimitMax="10"
                    ZoomLimitMin="1" />


                <!--  CACHED LAYER CONTROLS  -->
                <draw:SkiaLayer
                    UseCache="Operations"
                    VerticalOptions="Fill">


                        <draw:SkiaShape HorizontalOptions="Center"
                                    VerticalOptions="End"
                                    Margin="0,0,0,24"
                                    WidthRequest="300" HeightRequest="60"
                                    StrokeColor="Black"
                                    StrokeWidth="-1"
                                    BackgroundColor="#66000000"
                                    CornerRadius="32">


                        <draw:SkiaRow 
                            UseCache="GPU"
                            HorizontalOptions="Center" VerticalOptions="Center">


                            <!--POWER/PAUSE-->
                            <draw:SkiaShape 
                                Tapped="TappedTurnCamera"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="P" TextColor="White"/>

                            </draw:SkiaShape>

                            <!--EFFECTS-->
                            <draw:SkiaShape 
                                Tapped="TappedCycleEffects"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="E" TextColor="White"/>

                            </draw:SkiaShape>

                            <!--FLASH-->
                            <draw:SkiaShape 
                                Tapped="TappedFlash"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="F" TextColor="White"/>

                            </draw:SkiaShape>

                            <!--SOURCE/SWITCH-->
                            <draw:SkiaShape 
                                Tapped="TappedSwitchCamera"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="S" TextColor="White"/>

                            </draw:SkiaShape>

                            <draw:SkiaShape 
                                draw:AddGestures.CommandTapped="{Binding CommandCaptureStillPhoto}"
                                Type="Circle" HeightRequest="46" LockRatio="1" StrokeWidth="2" StrokeColor="Black" Padding="3">

                                <draw:SkiaShape 
                                    BackgroundColor="#330000"
                                    Type="Circle" HorizontalOptions="Fill" VerticalOptions="Fill"/>

                            </draw:SkiaShape>


                        </draw:SkiaRow>

                    </draw:SkiaShape>

                    <draw:SkiaHotspot
                        HorizontalOptions="Center"
                        IsVisible="{Binding ShowResume}"
                        LockRatio="1"
                        Tapped="TappedResume"
                        VerticalOptions="Center"
                        WidthRequest="290"
                        ZIndex="110" />

                </draw:SkiaLayer>

                <!--  catch pinch to zoom  -->
                <draw:SkiaHotspotZoom
                    ZoomMax="3"
                    ZoomMin="1"
                    Zoomed="OnZoomed" />

            </draw:SkiaLayer>

            <!--  SHADER SELECTOR DRAWER  -->
            <draw:SkiaDrawer
                Margin="0,0,0,24"
                x:Name="ShaderDrawer"
                HeaderSize="40"
                Direction="FromLeft"
                VerticalOptions="End"
                HorizontalOptions="Fill"
                HeightRequest="100"
                IsOpen="False"
                IgnoreWrongDirection="True"
                ZIndex="50">

                <!--  DRAWER CONTENT  -->
                <draw:SkiaShape
                    Type="Rectangle"
                    CornerRadius="0,12,12,0"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <!--  SHADER ITEMS SCROLL  -->
                        <draw:SkiaScroll
                            RespondsToGestures="{Binding Source={x:Reference ShaderDrawer}, Path=IsOpen}"
                            BackgroundColor="WhiteSmoke"
                            Margin="0,0,20,0"
                            Orientation="Horizontal"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"
                            Padding="8">

                            <controls:SkiaLayoutWithSelector
                                x:Name="Stack"
                                Type="Row"
                                VerticalOptions="Center"
                                Spacing="8"
                                RecyclingTemplate="Enabled"
                                UseCache="Operations"
                                ItemsSource="{Binding ShaderItems}">

                                <draw:SkiaLayout.ItemTemplate>
                                    <DataTemplate x:DataType="models:ShaderItem">

                                        <draw:SkiaShape
                                            Type="Rectangle"
                                            WidthRequest="80"
                                            HeightRequest="80"
                                            CornerRadius="8"
                                            BackgroundColor="White"
                                            UseCache="Image"
                                            draw:AddGestures.CommandLongPressing="{Binding Source={x:Reference CameraControl}, Path=CommandEditShader}"
                                            draw:AddGestures.CommandTapped="{Binding Source={x:Reference Stack}, Path=BindingContext.CommandSelectShader}">

                                            <!--  SHADER CELL CONTENT  -->
                                            <draw:SkiaLayout
                                                HorizontalOptions="Fill"
                                                VerticalOptions="Fill">

                                                <!--  IMAGE WITH SHADER EFFECT  -->
                                                <draw:SkiaImage
                                                    Source="{Binding ImageSource}"
                                                    ImageBitmap="{Binding  Source ={x:Reference ThisPage}, Path=DisplayPreview}"
                                                    Aspect="AspectCover"
                                                    HorizontalOptions="Fill"
                                                    VerticalOptions="Fill"
                                                    UseCache="Image">

                                                    <draw:SkiaImage.VisualEffects>
                                                        <draw:SkiaShaderEffect ShaderSource="{Binding ShaderFilename}" />
                                                    </draw:SkiaImage.VisualEffects>

                                                </draw:SkiaImage>

                                                <!--  SHADOW BACKGROUND FOR TEXT  -->
                                                <draw:SkiaShape
                                                    Type="Rectangle"
                                                    BackgroundColor="#80000000"
                                                    HorizontalOptions="Fill"
                                                    VerticalOptions="End"
                                                    HeightRequest="20" />

                                                <!--  TITLE LABEL  -->
                                                <draw:SkiaLabel
                                                    Text="{Binding Title}"
                                                    FontSize="12"
                                                    TextColor="White"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="End"
                                                    Margin="4"
                                                    HorizontalTextAlignment="Center"
                                                    UseCache="Operations" />

                                            </draw:SkiaLayout>

                                            <!--will not use shadows now-->
                                            <!--<draw:SkiaShape.Shadows>
                                            <draw:SkiaShadow
                                                Blur="4"
                                                Opacity="0.2"
                                                X="0"
                                                Y="2"
                                                Color="Black" />
                                        </draw:SkiaShape.Shadows>-->

                                        </draw:SkiaShape>
                                    </DataTemplate>
                                </draw:SkiaLayout.ItemTemplate>

                            </controls:SkiaLayoutWithSelector>

                        </draw:SkiaScroll>

                        <!--  DRAWER HEADER  -->
                        <draw:SkiaShape
                            Tapped="TappedDrawerHeader"
                            UseCache="Image"
                            HorizontalOptions="End"
                            Type="Rectangle"
                            BackgroundColor="WhiteSmoke"
                            CornerRadius="0,16,0,0"
                            VerticalOptions="Fill"
                            WidthRequest="41">

                            <draw:SkiaLayout
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaShape
                                    Type="Rectangle"
                                    WidthRequest="4"
                                    HeightRequest="40"
                                    BackgroundColor="#CCCCCC"
                                    CornerRadius="2"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center" />

                            </draw:SkiaLayout>

                        </draw:SkiaShape>

                    </draw:SkiaLayout>

                </draw:SkiaShape>

            </draw:SkiaDrawer>

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="Black"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</ContentPage>