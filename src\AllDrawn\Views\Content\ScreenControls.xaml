<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.ScreenControls"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:navigation1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:ProjectViewModel"
    BackgroundColor="Black"
    HorizontalOptions="Fill"
    Spacing="0"
    Type="Column"
    VerticalOptions="Fill">

    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="1"
            EndYRatio="1"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#889955</Color>
                <Color>#886655</Color>
                <Color>#222222</Color>
            </draw:SkiaGradient.Colors>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>

    <!--  NAVI BLOCK  -->
    <draw:SkiaLayout
        x:Name="NavBar"
        Spacing="0"
        Type="Column"
        HorizontalOptions="Fill"
        UseCache="Operations">

        <draw:SkiaControl
            HeightRequest="{Binding Presentation.StatusBarHeightRequest}"
            HorizontalOptions="Fill"
            Tag="StatusBar" />

        <navigation1:NavBarStandart BackgroundColor="Transparent" />

    </draw:SkiaLayout>

    <!--  CONTENT  -->
    <draw:SkiaScroll
        x:Name="MainScroll"
        HorizontalOptions="Fill"
        IgnoreWrongDirection="True"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            Padding="24"
            HorizontalOptions="Fill"
            Split="1"
            Tag="Wrapper"
            Type="Wrap"
            UseCache="None">

            <draw:SkiaLayout
                Margin="0,10,0,16"
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                Spacing="16"
                Split="0"
                Tag="Radio1"
                Type="Wrap"
                UseCache="ImageComposite">

                <controls:DrawnRadioButton Text="Flowers" />

                <controls:DrawnRadioButton HorizontalOptions="Start" Text="Food" />

                <controls:DrawnRadioButton Text="Origami" />

                <controls:DrawnRadioButton Text="Lego Bricks" />

                <controls:DrawnRadioButton Tag="CheckMe" Text="Something else" />

                <controls:DrawnRadioButton Text="Various" />

            </draw:SkiaLayout>

            <!--  SWITCHES  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                MinimumHeightRequest="60"
                Spacing="24"
                Split="0"
                Type="Wrap"
                UseCache="Operations">

                <controls:SkiaSwitchCupertino
                    ColorFrameOn="{StaticResource ColorAccent}"
                    ColorThumbOn="White" />

                <controls:SkiaSwitchMaterial
                    ColorFrameOff="DarkGray"
                    ColorFrameOn="{StaticResource ColorAccentLight}"
                    ColorThumbOff="White"
                    ColorThumbOn="{StaticResource ColorAccent}" />

                <controls:SkiaSwitchMaterialAir
                    ColorFrameOff="DarkGray"
                    ColorFrameOn="{StaticResource ColorAccentLight}"
                    ColorThumbOff="DarkGray"
                    ColorThumbOn="{StaticResource ColorAccent}" />

            </draw:SkiaLayout>

            <!--  SLIDERS  -->
            <draw:SkiaLayout
                Padding="50,24"
                BackgroundColor="#fefefe"
                HorizontalOptions="Fill"
                Split="1"
                Tag="Controls"
                Type="Wrap"
                UseCache="ImageComposite">


                <draw:SkiaLabel
                    Margin="0,16"
                    Padding="2"
                    BackgroundColor="#11000000"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="Simple Slider"
                    TextColor="Black" />

                <controls:SkiaSliderRanged
                    End="10"
                    HorizontalOptions="Fill"
                    Max="5000"
                    Min="10"
                    MinMaxStringFormat="C"
                    RangeMin="5"
                    SliderHeight="28"
                    Start="10"
                    Step="5"
                    ValueStringFormat="C" />

                <draw:SkiaLabel
                    Margin="0,16"
                    Padding="2"
                    BackgroundColor="#11000000"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="Range Slider"
                    TextColor="Black" />

                <controls:SkiaSliderRanged
                    EnableRange="True"
                    End="10000"
                    Max="10000"
                    Min="10"
                    MinMaxStringFormat="C"
                    RangeMin="5"
                    SliderHeight="28"
                    Start="10"
                    Step="5"
                    ValueStringFormat="C" />

                <draw:SkiaLabel
                    Margin="0,16"
                    Padding="2"
                    BackgroundColor="#11000000"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="Silly Slider"
                    TextColor="Black" />

                <controls:SkiaSliderFun
                    HorizontalOptions="Fill"
                    Max="100"
                    Min="0"
                    MinMaxStringFormat="P0"
                    SliderHeight="46"
                    Step="0.01"
                    UseCache="Operations" />

                <draw:SkiaLabel
                    Margin="0,16"
                    Padding="2"
                    BackgroundColor="#11000000"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="Tech Slider"
                    TextColor="Black" />

                <controls:SkiaSliderTech
                    HorizontalOptions="Fill"
                    Max="100"
                    Min="0"
                    MinMaxStringFormat="P0"
                    SliderHeight="40"
                    Step="0.01"
                    UseCache="Operations" />

            </draw:SkiaLayout>

            <draw:SkiaMarkdownLabel
                AddMarginBottom="60"
                AddMarginTop="16"
                FontFamily="FontText"
                FontSize="18"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Text="🚧 Under heavy construction 🚧"
                TextColor="#66ffffff"
                UseCache="Operations" />

        </draw:SkiaLayout>

    </draw:SkiaScroll>

</draw:SkiaLayout>
