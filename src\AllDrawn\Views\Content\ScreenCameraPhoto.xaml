<?xml version="1.0" encoding="utf-8" ?>
<content:CameraLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.ScreenCameraPhoto"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:content="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views"
    xmlns:controls="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    xmlns:camera="clr-namespace:DrawnUi.Camera;assembly=DrawnUi.Maui.Camera"
    Margin="0,0"
    x:DataType="viewModels:TakePictureViewModel"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <content:CameraLayout.Resources>
        <ResourceDictionary>

            <x:String x:Key="SvgShutter">
                <![CDATA[                                      
                <svg fill="#000000" width="800px" height="800px" viewBox="0 0 16 16" id="photo-camera-16px" xmlns="http://www.w3.org/2000/svg">
                  <path id="Path_62" data-name="Path 62" d="M-9.5,4h-1.154L-11.789.973A1.506,1.506,0,0,0-13.193,0h-3.614a1.506,1.506,0,0,0-1.4.973L-19.346,4H-20.5A2.5,2.5,0,0,0-23,6.5v7A2.5,2.5,0,0,0-20.5,16h11A2.5,2.5,0,0,0-7,13.5v-7A2.5,2.5,0,0,0-9.5,4Zm-7.775-2.675A.5.5,0,0,1-16.807,1h3.614a.5.5,0,0,1,.468.325l1,2.675h-6.556ZM-8,13.5A1.5,1.5,0,0,1-9.5,15h-11A1.5,1.5,0,0,1-22,13.5v-7A1.5,1.5,0,0,1-20.5,5h11A1.5,1.5,0,0,1-8,6.5ZM-15,6a4,4,0,0,0-4,4,4,4,0,0,0,4,4,4,4,0,0,0,4-4A4,4,0,0,0-15,6Zm0,7a3,3,0,0,1-3-3,3,3,0,0,1,3-3,3,3,0,0,1,3,3A3,3,0,0,1-15,13Zm.5-4.5A.5.5,0,0,1-15,9a1,1,0,0,0-1,1,.5.5,0,0,1-.5.5A.5.5,0,0,1-17,10a2,2,0,0,1,2-2A.5.5,0,0,1-14.5,8.5Z" transform="translate(23)"/>
                </svg>                                
                ]]>
            </x:String>

        </ResourceDictionary>
    </content:CameraLayout.Resources>


    <!--  CAMERA PREVIEW ETC  -->
    <camera:SkiaCamera
        x:Name="CameraControl"
        BackgroundColor="Black"
        CapturePhotoQuality="Medium"
        Facing="Default"
        HorizontalOptions="Fill"
        Tag="Camera"
        VerticalOptions="Fill"
        ZIndex="-1"
        ZoomLimitMax="10"
        ZoomLimitMin="1" />

    <!--  MASK  -->
    <!--<draw:SkiaHoverMask
        Margin="0"
        BackgroundColor="#33000000"
        CornerRadius="40"
        HorizontalOptions="Fill"
        LockRatio="-1"
        Tag="Mask"
        Type="Rectangle"
        VerticalOptions="Center" />-->

    <!--  CLOSE ICON  -->
    <draw:SkiaSvg
        x:Name="IconClose"
        Margin="0,56,32,0"
        HorizontalOptions="End"
        LockRatio="1"
        SvgString="{StaticResource SvgClose}"
        TintColor="White"
        WidthRequest="16" />

    <!--  CLOSE HOTSPOT  -->
    <draw:SkiaHotspot
        Margin="0,24,8,0"
        AnimationTapped="Ripple"
        CommandTapped="{Binding CommandCloseModal}"
        HeightRequest="40"
        HorizontalOptions="End"
        LockRatio="1"
        TransformView="{x:Reference IconClose}"
        VerticalOptions="Start"
        WidthRequest="75"
        ZIndex="1" />

    <!--  CONTROLS  -->
    <controls:SmallButton
        Margin="8,32,0,0"
        Tapped="TappedSwitchCamera"
        Text="Source"
        WidthRequest="100" />

    <controls:SmallButton
        Margin="8,80,0,0"
        IsVisible="{Binding IsDebug}"
        Tapped="TappedTurnCamera"
        Text="On/Off"
        WidthRequest="100" />

    <controls:SmallButton
        Margin="124,32,0,0"
        Tapped="TappedCycleEffects"
        Text="Effect"
        WidthRequest="100" />


    <!--  RESUME CAMERA  -->
    <!--<controls:SmallButton
            HorizontalOptions="Center"
            IsVisible="{Binding ShowResume}"
            Tapped="TappedResume"
            Text="Resume Camera"
            VerticalOptions="Center"
            WidthRequest="150"
            ZIndex="110" />-->

    <draw:SkiaHotspot
        HorizontalOptions="Center"
        IsVisible="{Binding ShowResume}"
        LockRatio="1"
        Tapped="TappedResume"
        VerticalOptions="Center"
        WidthRequest="290"
        ZIndex="110" />

    <!--  bottom tabs wrapper [cached]  -->
    <!--  we use padding to reserve space for shadow  -->
    <draw:SkiaLayout
        Padding="0,8,0,0"
        HorizontalOptions="Fill"
        IsClippedToBounds="True"
        UseCache="GPU"
        VerticalOptions="End"
        ZIndex="90">

        <!--  middle button CAPTURE  -->
        <draw:SkiaShape
            draw:AddGestures.CommandTapped="{Binding CommandCaptureStillPhoto}"
            BackgroundColor="{StaticResource ColorPrimary}"
            CornerRadius="20"
            HeightRequest="76"
            HorizontalOptions="Center"
            AddMarginBottom="16"
            VerticalOptions="Start"
            WidthRequest="76"
            ZIndex="96">

            <draw:SkiaSvg
                HeightRequest="32"
                HorizontalOptions="Center"
                LockRatio="1"
                SvgString="{x:StaticResource SvgShutter}"
                TintColor="{x:StaticResource ColorTextButton}"
                UseCache="Operations"
                VerticalOptions="Center" />

            <draw:SkiaShape.Shadows>

                <draw:SkiaShadow
                    Blur="8"
                    Opacity="0.33"
                    X="4"
                    Y="4"
                    Color="{StaticResource ColorPrimary}" />

            </draw:SkiaShape.Shadows>

        </draw:SkiaShape>

    </draw:SkiaLayout>

    <!--  catch pinch to zoom  -->
    <draw:SkiaHotspotZoom
        ZoomMax="3"
        ZoomMin="1"
        Zoomed="OnZoomed" />



</content:CameraLayout>
