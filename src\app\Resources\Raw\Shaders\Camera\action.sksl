﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the cool blue-green shadows with warm skin tones from <PERSON>
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>John <PERSON>ick styled color</returns>
half3 johnWickGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Separate shadows and highlights
    float shadowMask = 1.0 - smoothstep(0.2, 0.6, luminance);
    float highlightMask = smoothstep(0.4, 0.8, luminance);
    float midtoneMask = 1.0 - shadowMask - highlightMask;
    
    // Cool blue-green for shadows
    half3 shadowTone = half3(0.7, 0.9, 1.1);
    
    // Warm tone for skin/highlights
    half3 highlightTone = half3(1.2, 1.05, 0.9);
    
    // Neutral for midtones
    half3 midtoneTone = half3(1.0, 1.0, 1.0);
    
    // Apply tonal separation
    half3 gradedColor = color * (shadowTone * shadowMask + 
                                highlightTone * highlightMask + 
                                midtoneTone * midtoneMask);
    
    // Preserve luminance
    float newLuminance = dot(gradedColor, half3(0.299, 0.587, 0.114));
    gradedColor *= luminance / max(newLuminance, 0.001);
    
    // Increase contrast for that crisp action movie look
    gradedColor = (gradedColor - 0.5) * 1.15 + 0.5;
    
    // Crush blacks slightly for deeper shadows
    gradedColor = max(gradedColor - 0.02, 0.0) * 1.02;
    
    // Boost saturation moderately
    half3 finalColor = mix(half3(dot(gradedColor, half3(0.299, 0.587, 0.114))), gradedColor, 1.15);
    
    return clamp(finalColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 gradedColor = johnWickGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}