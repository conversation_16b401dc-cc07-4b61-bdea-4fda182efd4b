<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.ScreenItemDetails"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:navigation1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:ItemDetailsViewModel"
    BackgroundColor="Black"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">



    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="1"
            EndYRatio="1"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#886655</Color>
                <Color>#222222</Color>
            </draw:SkiaGradient.Colors>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>

    <!--  NAVI BLOCK  -->
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        Spacing="0"
        Type="Column">
        <draw:SkiaShape
            HeightRequest="{Binding Presentation.StatusBarHeightRequest}"
            HorizontalOptions="Fill"
            Tag="StatusBar" />
        <navigation1:NavBarStandart BackgroundColor="Transparent" />
    </draw:SkiaLayout>





</draw:SkiaLayout>
