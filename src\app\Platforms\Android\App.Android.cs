﻿using Android.Content;
using Microsoft.Maui.Controls.PlatformConfiguration;

namespace ShadersCamera;

public partial class App : Application
{
        public static void Test()
        {

            // string redirectLink = "mirpay://pay.mironline.ru/inapp/*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/#Intent;scheme=mirpay;package=ru.nspk.mirpay;end";
            //
            // try
            // {
            //         var intent = new Intent(Intent.ActionView,Android.Net.Uri.Parse(redirectLink));
            //         intent.AddFlags(ActivityFlags.NewTask);
            //         Android.App.Application.Context.StartActivity(intent);
            //
            // }
            // catch (Exception e)
            // {
            //         Console.WriteLine(e); 
            // }
        }
        
}
