﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

// ===============================================
// INJECTED 200MM LENS FUNCTIONS
// ===============================================

/// <summary>
/// Creates perspective compression typical of 200mm telephoto lenses
/// </summary>
/// <param name="uv">Original UV coordinates</param>
/// <returns>Compressed perspective coordinates</returns>
float2 telephotoCompression(float2 uv) {
    float2 centered = uv - 0.5;
    float distance = length(centered);
    
    // Compress TOWARDS center (pulls outer pixels inward - makes noses smaller!)
    float compressionStrength = 0.05;

    float compressionFactor = 1.0 + distance * compressionStrength;
    float2 compressed = centered / compressionFactor;
    
    // Calculate zoom needed to fill frame after compression
    // Maximum compression happens at corners (distance ~0.707)
    float maxDistance = 0.707; // sqrt(0.5^2 + 0.5^2)
    float maxCompressionFactor = 1.0 + maxDistance * compressionStrength;
    
    // Calculate zoom needed to fill frame after compression (cut in half)
    float zoomToFill = 1.0 + (maxCompressionFactor - 1.0) * 0.5; // Half way between no zoom and full zoom
    
    // Apply gentle zoom IN to partially fill black borders
    compressed /= zoomToFill;  // DIVIDE to zoom IN (magnify center area)
    
    return 0.5 + compressed;
}

/// <summary>
/// Simulates shallow depth of field from 200mm lens
/// </summary>
/// <param name="coord">Screen coordinate</param>
/// <param name="uv">UV coordinates</param>
/// <returns>Focus-blurred color</returns>
half3 shallowDepthOfField(float2 coord, float2 uv) {

    float blurStrength = 1.0;


    float2 focusCenter = float2(0.5, 0.5);
    float distanceFromFocus = length(uv - focusCenter);
    
    float blurRadius = smoothstep(0.15, 0.45, distanceFromFocus) * blurStrength;
    
    if (blurRadius < 0.5) {
        return iImage1.eval(coord).rgb;
    }
    
    half3 blurred = half3(0.0);
    float totalWeight = 0.0;
    
    for (int i = -2; i <= 2; i++) {
        for (int j = -2; j <= 2; j++) {
            float2 offset = float2(float(i), float(j)) * blurRadius;
            float weight = exp(-dot(offset, offset) * 0.1);
            blurred += iImage1.eval(coord + offset).rgb * weight;
            totalWeight += weight;
        }
    }
    
    return blurred / totalWeight;
}

// ===============================================
// ORIGINAL STREET B&W FILM FUNCTIONS (UNCHANGED)
// ===============================================

/// <summary>
/// Generates heavy grain typical of pushed film
/// </summary>
/// <param name="coord">Screen coordinate</param>
/// <returns>Heavy grain intensity</returns>
float heavyGrain(float2 coord) {
    float2 grainCoord = coord * 1.2;
    float noise1 = fract(sin(dot(grainCoord, float2(127.1, 311.7))) * 43758.5453);
    float noise2 = fract(sin(dot(grainCoord * 1.3, float2(269.5, 183.3))) * 17951.3421);
    float noise3 = fract(sin(dot(grainCoord * 0.7, float2(419.2, 371.9))) * 31415.9265);
    return (noise1 + noise2 + noise3 - 1.5) * 0.04;
}

/// <summary>
/// Applies pushed Tri-X style gritty B&W conversion
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Gritty street B&W value</returns>
float pushedTriXConversion(half3 color) {
    float luminance = dot(color, half3(0.4, 0.4, 0.2));
    
    luminance = pow(luminance, 0.8);
    luminance = (luminance - 0.5) * 1.8 + 0.5;
    
    float halation = smoothstep(0.7, 1.0, luminance) * 0.1;
    luminance += halation;
    
    luminance = max(luminance - 0.05, 0.0) * 1.05;
    
    return clamp(luminance, 0.0, 1.0);
}

/// <summary>
/// Creates harsh vignette for urban feel
/// </summary>
/// <param name="uv">UV coordinates</param>
/// <returns>Urban vignette multiplier</returns>
float urbanVignette(float2 uv) {
    float2 centered = uv - 0.5;
    float distance = length(centered);
    return 1.0 - smoothstep(0.4, 0.9, distance * 1.2);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    float2 uv = inputCoord / iImageResolution.xy;
    
    // ===============================================
    // INJECTED LENS PROCESSING (APPLIED FIRST)
    // ===============================================
    
    // Apply 200mm perspective compression
    float2 compressedUV = telephotoCompression(uv);
    float2 compressedCoord = compressedUV * iImageResolution.xy;
    
    // Get color with shallow depth of field
    half3 originalColor = shallowDepthOfField(compressedCoord, compressedUV);
    
    // ===============================================
    // ORIGINAL STREET FILM PROCESSING (UNCHANGED LOGIC)
    // ===============================================
    
    float bwValue = pushedTriXConversion(originalColor);
    
    // Add heavy grain (using compressed coordinates)
    float grain = heavyGrain(compressedCoord);
    bwValue += grain;
    
    // Apply urban vignette (using ORIGINAL UV - vignette stays centered!)
    float vignette = urbanVignette(uv);
    bwValue *= vignette * 0.85 + 0.15;
    
    bwValue = clamp(bwValue, 0.0, 1.0);
    
    return half4(bwValue, bwValue, bwValue, 1.0);
}