﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

// Cool retro game parameters that kids will actually like:
const float PIXEL_SIZE = 10.0;           
const float COLOR_LEVELS = 32.0;     
const float WARMTH_BOOST = 1.08;      // makes skin look better
const float RETRO_SATURATION = 1.25;  // Boost colors but keep it natural
const float CONTRAST_ENHANCE = 1.5;   // Subtle contrast for crisp look

half4 main(float2 fragCoord) {
    // 1. Map fragment coordinate to input image coordinate space
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    // 2. Smooth pixelation - less harsh, more stylized
    float2 pixelPos = floor(inputCoord / PIXEL_SIZE) * PIXEL_SIZE;
    float2 pixelCenter = pixelPos + PIXEL_SIZE * 0.5;
    
    // 3. Sample with slight blur for smoother pixels
    half4 color = iImage1.eval(pixelCenter);
    half4 blendColor = iImage1.eval(pixelPos + PIXEL_SIZE * 0.25);
    color = mix(color, blendColor, 0.15); // Subtle blend for smoother look
    
    // 4. Enhance contrast gently
    color.rgb = mix(color.rgb, (color.rgb - 0.5) * CONTRAST_ENHANCE + 0.5, 0.7);
    
    // 5. Add warm, appealing tint (like Instagram filters kids love)
    color.r *= WARMTH_BOOST;
    color.g *= (WARMTH_BOOST * 0.98);
    
    // 6. Smart color quantization that preserves skin tones
    half3 quantized = floor(color.rgb * COLOR_LEVELS + 0.5) / COLOR_LEVELS;
    
    // 7. Boost saturation but keep it natural
    half luminance = dot(quantized, half3(0.299, 0.587, 0.114));
    quantized = mix(half3(luminance), quantized, RETRO_SATURATION);
    
    // 8. Add subtle "retro game" edge definition
    float edgeOffset = PIXEL_SIZE * 0.5;
    half4 rightPixel = iImage1.eval(pixelCenter + float2(edgeOffset, 0));
    half4 bottomPixel = iImage1.eval(pixelCenter + float2(0, edgeOffset));
    
    half edgeDetection = length(color.rgb - rightPixel.rgb) + length(color.rgb - bottomPixel.rgb);
    half edgeEnhance = clamp(edgeDetection * 3.0, 0.0, 0.15);
    quantized = mix(quantized, quantized * 1.1, edgeEnhance);
    
    // 9. Add subtle animated "digital" sparkle effect for fun
    float2 sparkleCoord = floor(fragCoord / 8.0);
    float sparkleNoise = fract(sin(dot(sparkleCoord, float2(12.9898, 78.233)) + iTime * 0.5) * 43758.5453);
    if (sparkleNoise > 0.98 && luminance > 0.7) {
        quantized += 0.1; // Subtle highlight sparkle on bright areas
    }
    
    // 10. Final touch - ensure colors stay appealing
    quantized = clamp(quantized, 0.0, 1.0);
    
    return half4(quantized, color.a);
}