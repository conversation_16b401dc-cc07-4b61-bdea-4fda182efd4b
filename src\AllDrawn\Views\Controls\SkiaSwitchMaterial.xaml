<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaSwitch
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.Controls.SkiaSwitchMaterial"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisControl"
    Padding="3"
    HeightRequest="30"
    UseCache="Image"
    WidthRequest="56">

    <draw:SkiaShape
        Margin="4,4"
        CornerRadius="20"
        HorizontalOptions="Fill"
        Tag="Frame"
        Type="Rectangle"
        VerticalOptions="Fill" />

    <draw:SkiaShape
        x:Name="ThumbRef"
        HorizontalOptions="Start"
        LockRatio="-1"
        Tag="Thumb"
        Type="Circle"
        VerticalOptions="Fill">

        <draw:SkiaShape.Shadows>

            <draw:SkiaShadow
                Blur="2"
                Opacity="0.5"
                X="1"
                Y="1"
                Color="Black" />

        </draw:SkiaShape.Shadows>

    </draw:SkiaShape>

    <draw:SkiaHotspot
        Tapped="OnTapped"
        TransformView="{x:Reference ThumbRef}" />

</draw:SkiaSwitch>

