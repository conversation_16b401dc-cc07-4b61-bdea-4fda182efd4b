<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaSlider
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.Controls.SkiaSliderRanged"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisSlider"
    AvailableWidthAdjustment="-2"
    HorizontalOptions="Fill"
    RowDefinitions="40, Auto,32"
    RowSpacing="0"
    Type="Grid"
    UseCache="Image">

    <!--  AvailableWidthAdjustment tuned for thumb shadow  -->

    <draw:SkiaSlider.Resources>
        <ResourceDictionary>

            <x:String x:Key="SvgThumb">
                <![CDATA[ 
                                     
 <?xml version="1.0" encoding="utf-8"?>
<svg width="11px" height="11px" viewBox="0 0 11 11" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg">
  <desc>Created with Lunacy</desc>
  <g id="thumb" transform="translate(0.5 0.5)">
    <g id="thumb">
      <g id="thumb">
        <g id="g12">
          <g id="g10" transform="matrix(1.333333 0 0 -1.333333 0 9.220332)">
            <path d="M0 3.45763C0 1.54803 1.54803 0 3.45763 0C5.36722 0 6.91525 1.54803 6.91525 3.45763C6.91525 5.36722 5.36722 6.91525 3.45763 6.91525C1.54803 6.91525 0 5.36722 0 3.45763L0 3.45763Z" transform="matrix(1 0 0 -1 0 6.91525)" id="path824" fill="#F740A0" stroke="#26003D" stroke-width="1.0" />
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
                                
                ]]>

            </x:String>

            <Color x:Key="ColorMixedLight">#976ea0</Color>

        </ResourceDictionary>
    </draw:SkiaSlider.Resources>

    <!--  VALUE LABELS  -->

    <!--
        we predefine height so that stack height doesn't change
        dinamically upon text size inside labels, to improve performance
    -->

    <draw:SkiaLayout HeightRequest="40" HorizontalOptions="Fill">

        <!--  START LABEL - RANGED ONLY  -->
        <draw:SliderValueDesc
            x:Name="StartDesc"
            Padding="0"
            HorizontalOptions="Start"
            IsVisible="False"
            UseCache="Image"
            VerticalOptions="Fill"
            XCenter="{Binding Source={x:Reference StartThumb}, Path=XCenter}"
            XMaxLimit="{Binding Source={x:Reference EndDesc}, Path=TranslationX}"
            XMinLimit="0">

            <draw:SkiaControl.Triggers>
                <DataTrigger
                    Binding="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
                    TargetType="draw:SkiaControl"
                    Value="True">
                    <Setter Property="IsVisible" Value="True" />
                </DataTrigger>
            </draw:SkiaControl.Triggers>

            <!--  cached background bubble  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaShape
                    Margin="1"
                    BackgroundColor="{StaticResource ColorAccent}"
                    CornerRadius="4"
                    HeightRequest="28"
                    HorizontalOptions="Fill"
                    StrokeColor="Black"
                    StrokeWidth="1" />

            </draw:SkiaLayout>

            <!--  label overlay  -->
            <draw:SkiaLabel
                Margin="6,4"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                MaxLines="1"
                MinimumWidthRequest="38"
                Text="{Binding Source={x:Reference ThisSlider}, Path=StartDesc}"
                TextColor="{StaticResource Gray100}" />

        </draw:SliderValueDesc>

        <!--  END LABEL  -->
        <draw:SliderValueDesc
            x:Name="EndDesc"
            Padding="0"
            HorizontalOptions="Start"
            Tag="EndLabel"
            UseCache="Image"
            VerticalOptions="Fill"
            XCenter="{Binding Source={x:Reference EndThumb}, Path=XCenter}"
            XMaxLimit="{Binding Source={x:Reference ThisSlider}, Path=Width}"
            XMinLimit="{Binding Source={x:Reference StartDesc}, Path=RightX}">

            <!--  cached background bubble  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Tag="Bubble"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaShape
                    Tag="EndLabelShape"
                    Margin="1"
                    BackgroundColor="{StaticResource ColorAccent}"
                    CornerRadius="4"
                    HeightRequest="28"
                    HorizontalOptions="Fill"
                    StrokeColor="Black"
                    StrokeWidth="1" />

            </draw:SkiaLayout>

            <!--  label overlay  -->
            <draw:SkiaLabel
                Margin="6,4"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                MaxLines="1"
                MinimumWidthRequest="38"
                Tag="LabelEndText"
                Text="{Binding Source={x:Reference ThisSlider}, Path=EndDesc}"
                TextColor="{StaticResource Gray100}" />

        </draw:SliderValueDesc>

    </draw:SkiaLayout>

    <!--  MAIN GRID  -->
    <draw:SkiaLayout
        x:Name="ThisSliderGrid"
        Grid.Row="1"
        Margin="0,8,0,0"
        HeightRequest="{Binding Source={x:Reference ThisSlider}, Path=SliderHeight}"
        HorizontalOptions="Fill"
        Tag="Trail"
        VerticalOptions="Start">

        <!--  TRAIL  -->
        <draw:SkiaShape
            BackgroundColor="Gray"
            HeightRequest="2"
            HorizontalOptions="Fill"
            UseCache="Operations"
            VerticalOptions="Center" />

        <!--  SELECTED TRAIL  -->
        <draw:SliderTrail
            BackgroundColor="{StaticResource ColorPrimary}"
            HeightRequest="7"
            HorizontalOptions="Start"
            VerticalOptions="Center"
            XPos="{Binding Source={x:Reference StartThumb}, Path=TranslationX}"
            XPosEnd="{Binding Source={x:Reference EndThumb}, Path=TranslationX}" />

        <!--  START POINT  -->
        <draw:SkiaShape
            BackgroundColor="Gray"
            HeightRequest="13"
            HorizontalOptions="Start"
            LockRatio="1"
            Type="Circle"
            UseCache="Image"
            VerticalOptions="Center">
            <draw:SkiaShape.Triggers>
                <DataTrigger
                    Binding="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
                    TargetType="draw:SkiaShape"
                    Value="True">
                    <Setter Property="BackgroundColor" Value="Gray" />
                </DataTrigger>
                <DataTrigger
                    Binding="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
                    TargetType="draw:SkiaShape"
                    Value="False">
                    <Setter Property="BackgroundColor" Value="{StaticResource ColorAccent}" />
                </DataTrigger>
            </draw:SkiaShape.Triggers>
        </draw:SkiaShape>

        <!--  END POINT  -->
        <draw:SkiaShape
            BackgroundColor="Gray"
            HeightRequest="13"
            HorizontalOptions="End"
            LockRatio="1"
            Type="Circle"
            UseCache="Image"
            VerticalOptions="Center" />

        <!--  START THUMB - RANGED ONLY  -->
        <draw:SliderThumb
            x:Name="StartThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            IsVisible="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=StartThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="4"
                BackgroundColor="{StaticResource ColorPrimary}"
                HorizontalOptions="Fill"
                Type="Circle"
                VerticalOptions="Fill">
                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="Black" />

                </draw:SkiaShape.Shadows>
            </draw:SkiaShape>

        </draw:SliderThumb>

        <!--  END THUMB  -->
        <draw:SliderThumb
            x:Name="EndThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            HorizontalOptions="Start"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=EndThumbX}"
            UseCache="Image"
            VerticalOptions="Start"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="4"
                BackgroundColor="{StaticResource ColorPrimary}"
                HorizontalOptions="Fill"
                Type="Circle"
                VerticalOptions="Fill">
                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="2"
                        Opacity="0.5"
                        X="1"
                        Y="1"
                        Color="Black" />

                </draw:SkiaShape.Shadows>
            </draw:SkiaShape>

        </draw:SliderThumb>

    </draw:SkiaLayout>

    <!--  BOTTOM INFO  -->
    <draw:SkiaLayout
        Grid.Row="2"
        Margin="0,2,0,0"
        HorizontalOptions="Fill"
        UseCache="Operations"
        VerticalOptions="Start">

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="Start"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MinDesc}"
            TextColor="Black" />

        <draw:SkiaLabel
            FontSize="13"
            HorizontalOptions="End"
            Text="{Binding Source={x:Reference ThisSlider}, Path=MaxDesc}"
            TextColor="Black" />

    </draw:SkiaLayout>

</draw:SkiaSlider>
