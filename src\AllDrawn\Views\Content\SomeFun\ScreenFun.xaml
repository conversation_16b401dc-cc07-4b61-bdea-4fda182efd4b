﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Reversi.Views.Partials.ScreenFun"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:LakeViewModel"
    BackgroundColor="DarkBlue"
    HorizontalOptions="Fill"
    Tag="CanvasLake"
    VerticalOptions="Fill">

    <controls:ScrollingBanner
        Padding="0,2"
        IsBanner="True"
        Orientation="Horizontal"
        Tag="Banner">

        <!--  notice cache inside scroll and its impact on fps  -->
        <draw:SkiaLayout
            Tag="Banner"
            UseCache="Operations">

            <draw:SkiaLabel
                FontFamily="FontTextTitle"
                FontSize="50"
                Tag="Banner"
                Text="Drawn Ui - draw and animate with .Net Maui using SkiaSharp.."
                TextColor="#feff00">
                <draw:SkiaLabel.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="0"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>White</Color>
                            <Color>Yellow</Color>
                            <Color>Orange</Color>
                            <Color>Red</Color>
                            <Color>DarkRed</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaLabel.FillGradient>
            </draw:SkiaLabel>

        </draw:SkiaLayout>
    </controls:ScrollingBanner>

    <controls:Lake
        Margin="0,0,0,75"
        HorizontalOptions="Fill"
        Tag="Lake"
        UseCache="Operations"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            Padding="24,0"
            AddMarginTop="80"
            HorizontalOptions="Fill"
            Opacity="0.5"
            Split="1"
            Type="Wrap"
            UseCache="GPU"
            ZIndex="-1">

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="Create your custom controls: subclass `SkiaScrollLooped` to create a banner on top, for jumping letters subclass a `SkiaLabel` to dynamically offset characters Y with a subclassed `SkiaValueAnimator` that would implement pendulum physics." />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="Maui Robot is animated in the same manner, responding to gestures passed over from the canvas." />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="The whole screen layout was defined using XAML." />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="Reselect bottom tabs icon to exit to root." />

        </draw:SkiaLayout>

        <controls:DroppingLetters
            Margin="24"
            CharacterSpacing="2"
            FontFamily="FontTextTitle"
            FontSize="14"
            HorizontalOptions="Center"
            IsVisible="True"
            Tag="LabelWelcome"
            Text="HELLO FROM DRAWN UI!"
            UseCache="Operations"
            VerticalOptions="End">
            <draw:SkiaLabel.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="0"
                    EndYRatio="1"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#fffeff00</Color>
                        <Color>Gold</Color>
                        <Color>Orange</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaLabel.FillGradient>
        </controls:DroppingLetters>

        <draw:SkiaLayout
            Tag="Duck"
            UseCache="GPU">

            <!--  SvgString="{StaticResource SvgDuck}"  -->

            <draw:SkiaSvg
                HeightRequest="50"
                HorizontalOptions="Center"
                LockRatio="1"
                Source="Svg/dotnet_bot.svg" />

            <draw:SkiaLabel
                AddMarginTop="50"
                FontFamily="FontTextTitle"
                HorizontalOptions="Center"
                MaxLines="1"
                Tag="LabelDrag"
                Text="Drag Me!"
                TextColor="GreenYellow"
                VerticalOptions="End"
                ZIndex="-1" />

        </draw:SkiaLayout>

    </controls:Lake>



</draw:SkiaLayout>
