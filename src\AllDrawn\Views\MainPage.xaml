<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:content="using:AppoMobi.Maui.DrawnUi.Demo.Views"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:navigation="using:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:MainPageViewModel"
    BackgroundColor="Black"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <!--
        ScaleX="0.6"
        CameraAngleX="0"
        CameraAngleY="20"
        CameraAngleZ="0"
        ScaleY="0.6"
        TransformPivotPointX="1"
        TransformPivotPointY="0.5"
        TranslationX="-140"
        UseCache="GPU"
    -->

    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="0.5"
            EndYRatio="0.8"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#886655</Color>
                <Color>#222222</Color>
            </draw:SkiaGradient.Colors>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>

    <draw:SkiaViewSwitcher
        x:Name="Switcher"
        AnimateTabs="True"
        HorizontalOptions="Fill"
        IsClippedToBounds="True"
        SelectedIndex="{Binding SelectedIndex, Mode=TwoWay}"
        TabsAnimationEasing="{x:Static Easing.CubicOut}"
        TabsAnimationSpeed="150"
        Tag="NavigationLayout"
        VerticalOptions="Fill">

        <content:TabScrollCells />
        <content:TabWithTopTabs />
        <content:TabButtons />

    </draw:SkiaViewSwitcher>

    <!--  cannot cache with blurred tabs changes with every scrolling  -->
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        IsClippedToBounds="True"
        VerticalOptions="End"
        ZIndex="90">

        <!--  static shadow+texture  -->
        <!--  cached GPU  -->
        <draw:SkiaLayout
            Margin="0,0,0,-2"
            Padding="0,16,0,0"
            HorizontalOptions="Fill"
            UseCache="GPU"
            VerticalOptions="Fill"
            ZIndex="-1">

            <draw:SkiaShape
                BackgroundColor="#20cccccc"
                ClipBackgroundColor="True"
                CornerRadius="20,20,0, 0"
                HorizontalOptions="Fill"
                StrokeColor="#20cccccc"
                StrokeWidth="2"
                VerticalOptions="Fill">

                <!--  texture  to mix with  -->
                <draw:SkiaImage
                    AddEffect="Darken"
                    Aspect="AspectCover"
                    Darken="0.35"
                    HorizontalOptions="Fill"
                    Opacity="0.4"
                    Source="Pics/glass2.jpg"
                    VerticalOptions="Fill" />

                <draw:SkiaShape.StrokeGradient>

                    <draw:SkiaGradient
                        EndXRatio="0.905"
                        EndYRatio="1"
                        StartXRatio="0.9"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#99ffffff</Color>
                            <Color>#222222</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.StrokeGradient>
                <draw:SkiaShape.Shadows>
                    <draw:SkiaShadow
                        Blur="5"
                        Opacity="0.5"
                        X="1"
                        Y="-1"
                        Color="#000000" />

                </draw:SkiaShape.Shadows>

            </draw:SkiaShape>
        </draw:SkiaLayout>


        <!--  cached GPU  -->
        <navigation:BottomTabsSelector
            x:Name="Tabs"
            Margin="0,0,0,2"
            AddMarginTop="16"
            ColumnDefinitions="*,*,*"
            ColumnSpacing="0"
            CommandTabReselected="{Binding CommandTabReselected}"
            HorizontalOptions="Fill"
            RowSpacing="0"
            SelectedIndex="{Binding Source={x:Reference Switcher}, Path=SelectedIndex, Mode=TwoWay}"
            Tag="Tabs"
            Type="Grid"
            UseCache="GPU">

            <draw:SkiaLayout.RowDefinitions>
                <RowDefinition Height="{Binding Presentation.BottomTabsHeightRequest}" />
                <RowDefinition Height="{Binding Presentation.PaddingBottom}" />
            </draw:SkiaLayout.RowDefinitions>

            <!--  <draw:SkiaControl  -->
            <!--  HorizontalOptions="Fill"  -->
            <!--  Grid.ColumnSpan="3"  -->
            <!--  BackgroundColor="Black"  -->
            <!--  Grid.Row="1"/>  -->

            <draw:SkiaSvg
                HeightRequest="28"
                HorizontalOptions="Center"
                IsClippedToBounds="True"
                LockRatio="1"
                SvgString="{x:StaticResource SvgHome}"
                TintColor="White"
                UseCache="Operations"
                VerticalOptions="Center" />

            <draw:SkiaHotspot
                AnimationTapped="None"
                CommandTapped="{Binding Source={x:Reference Tabs}, Path=CommandTappedTab}"
                CommandTappedParameter="0"
                ZIndex="3" />

            <draw:SkiaSvg
                Grid.Column="1"
                HeightRequest="29"
                HorizontalOptions="Center"
                IsClippedToBounds="True"
                LockRatio="1"
                SvgString="{x:StaticResource SvgChat}"
                TintColor="White"
                UseCache="Operations"
                VerticalOptions="Center" />

            <draw:SkiaHotspot
                Grid.Column="1"
                AnimationTapped="None"
                CommandTapped="{Binding Source={x:Reference Tabs}, Path=CommandTappedTab}"
                CommandTappedParameter="1"
                ZIndex="3" />

            <draw:SkiaSvg
                Grid.Column="2"
                HeightRequest="28"
                HorizontalOptions="Center"
                IsClippedToBounds="True"
                LockRatio="1"
                SvgString="{x:StaticResource SvgSettings}"
                TintColor="White"
                UseCache="Operations"
                VerticalOptions="Center" />

            <draw:SkiaHotspot
                Grid.Column="2"
                AnimationTapped="None"
                CommandTapped="{Binding Source={x:Reference Tabs}, Path=CommandTappedTab}"
                CommandTappedParameter="2"
                ZIndex="3" />

            <navigation:TabsBall
                Grid.ColumnSpan="3"
                BackgroundColor="#000000FF"
                CircleRadius="3"
                SelectedIndex="{Binding Source={x:Reference Switcher}, Path=SelectedIndex, Mode=OneWay}"
                TabsCount="3"
                VerticalOffset="20"
                Color="{StaticResource ColorAccent}" />

        </navigation:BottomTabsSelector>


        <!--  SkiaBackdrop  -->
        <!--  cached never  -->
        <draw:SkiaShape
            Margin="0,16,0,-2"
            CornerRadius="20,20,0, 0"
            HorizontalOptions="Fill"
            IsVisible="{Binding Presentation.HasBlur}"
            VerticalOptions="Fill"
            ZIndex="-2">

            <draw:SkiaBackdrop
                Blur="10"
                HorizontalOptions="Fill"
                Tag="Blur"
                VerticalOptions="Fill" />

        </draw:SkiaShape>

    </draw:SkiaLayout>

</draw:SkiaLayout>
