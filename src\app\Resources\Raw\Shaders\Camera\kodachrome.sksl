﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the rich, saturated Kodachrome film stock look
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Kodachrome styled color</returns>
half3 kodachromeGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Kodachrome characteristic warm shift
    half3 warmShift = color;
    warmShift.r *= 1.25;
    warmShift.g *= 1.15;
    warmShift.b *= 0.85;
    
    // Boost saturation significantly for that rich film look
    half3 saturatedColor = mix(half3(luminance), warmShift, 1.8);
    
    // Kodachrome S-curve for film-like contrast
    saturatedColor.r = saturatedColor.r * saturatedColor.r * (3.0 - 2.0 * saturatedColor.r);
    saturatedColor.g = saturatedColor.g * saturatedColor.g * (3.0 - 2.0 * saturatedColor.g);
    saturatedColor.b = saturatedColor.b * saturatedColor.b * (3.0 - 2.0 * saturatedColor.b);
    
    // Lift shadows slightly for that vintage look
    saturatedColor += 0.05;
    
    // Compress highlights to avoid blown out areas
    float highlightMask = smoothstep(0.7, 1.0, luminance);
    saturatedColor = mix(saturatedColor, saturatedColor * 0.85 + 0.15, highlightMask);
    
    // Add characteristic Kodachrome color shifts
    // Enhance reds and oranges
    float warmMask = smoothstep(0.3, 0.8, saturatedColor.r / max(saturatedColor.b + 0.001, 0.001));
    saturatedColor.r *= mix(1.0, 1.3, warmMask);
    saturatedColor.g *= mix(1.0, 1.1, warmMask);
    
    // Enhance blues and cyans slightly
    float coolMask = smoothstep(0.3, 0.8, saturatedColor.b / max(saturatedColor.r + 0.001, 0.001));
    saturatedColor.b *= mix(1.0, 1.2, coolMask);
    
    return clamp(saturatedColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 gradedColor = kodachromeGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}