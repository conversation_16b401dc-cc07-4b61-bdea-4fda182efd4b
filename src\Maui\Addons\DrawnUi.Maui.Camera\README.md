﻿# SkiaCamera

A totally drawn camera control for .NET MAUI with manual camera selection support.
Supports Android, iOS, MacCatalyst, Windows.

* Provides preview frames and still capture results for further processing.
Easily pass images to AI/ML.

* Renders live preview on a SkiaSharp canvas with hardware acceleration.
Apply shaders, adjustments and transforms to camera preview in realtime and draw anything over.

* **NEW**: Manual camera selection - choose any available camera by index, perfect for devices with multiple cameras.

## Platform Support

| Platform | Status | Implementation |
|----------|--------|----------------|
| Android | ✅ Complete | Camera2 API with CameraX |
| iOS | ✅ Complete | AVFoundation (shared with macCatalyst) |
| MacCatalyst | ✅ Complete | AVFoundation (shared with iOS) |
| Windows | ✅ Complete | MediaCapture with WinRT APIs |

## Installation

```
//todo add drawnui.maui.camera nuget etc
```

## Set up permissions

### Windows:

No specific setup needed.

### Apple:

Put this inside the file `Platforms/iOS/Info.plist` and `Platforms/MacCatalyst/Info.plist` inside the `<dict>` tag:

```xml
  <key>NSCameraUsageDescription</key>
  <string>Allow access to the camera</string>	
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow access to the library to save photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to the library to save photos</string>
```

If you want to geo-tag photos (get and save GPS location metadata) add this:

```xml
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To be able to geotag photos</string>
```

### Android

Put this inside the file `Platforms/Android/AndroidManifest.xml` inside the `<manifest>` tag:

```xml
    <!--for camera and gallery access-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.CAMERA" />
```

If you want to geo-tag photos (get and save GPS location metadata) add this:

```xml
  <!--geotag photos-->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

## Complete Usage Guide

### 1. XAML Declaration

```xml
<camera:SkiaCamera
    x:Name="CameraControl"
    BackgroundColor="Black"
    CapturePhotoQuality="Medium"
    Facing="Default"
    HorizontalOptions="Fill"
    VerticalOptions="Fill"
    ZoomLimitMax="10"
    ZoomLimitMin="1" />
```

### 2. Essential Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `Facing` | `CameraPosition` | `Default` | Camera selection: `Default` (back), `Selfie` (front), `Manual` |
| `CameraIndex` | `int` | `-1` | Manual camera selection index (when `Facing = Manual`) |
| `IsOn` | `bool` | `false` | Camera power state - use this to start/stop camera |
| `CapturePhotoQuality` | `CaptureQuality` | `Max` | Photo quality: `Max`, `Medium`, `Low`, `Preview` |
| `Effect` | `SkiaImageEffect` | `None` | Real-time effects: `None`, `Sepia`, `BlackAndWhite`, `Pastel` |
| `Zoom` | `double` | `1.0` | Camera zoom level |
| `ZoomLimitMin/Max` | `double` | `1.0/10.0` | Zoom constraints |
| `State` | `CameraState` | `Off` | Current camera state (read-only) |
| `IsBusy` | `bool` | `false` | Whether camera is processing (read-only) |

### 3. Camera Lifecycle Management

```csharp
// ✅ CORRECT: Use IsOn for lifecycle management
camera.IsOn = true;  // Start camera
camera.IsOn = false; // Stop camera

// ❌ AVOID: Direct Start() calls unless you know what you're doing
// camera.Start(); // Use only in special scenarios
```

**Important**: `IsOn` vs `Start()` difference:
- `IsOn = true`: Proper lifecycle management, handles permissions, app backgrounding
- `Start()`: Direct method call, bypasses safety checks

### 4. Camera Selection

#### Automatic Selection (Default)
```csharp
// Back camera
camera.Facing = CameraPosition.Default;

// Front camera
camera.Facing = CameraPosition.Selfie;

// Switch between front/back
private void SwitchCamera()
{
    if (camera.IsOn)
    {
        camera.Facing = camera.Facing == CameraPosition.Selfie
            ? CameraPosition.Default
            : CameraPosition.Selfie;
    }
}
```

#### Manual Camera Selection (NEW)
```csharp
// Get available cameras
var cameras = await camera.GetAvailableCamerasAsync();

// List all cameras
foreach (var cam in cameras)
{
    Console.WriteLine($"Camera {cam.Index}: {cam.Name} ({cam.Position}) Flash: {cam.HasFlash}");
}

// Select specific camera (e.g., ultra-wide, telephoto)
camera.Facing = CameraPosition.Manual;
camera.CameraIndex = 2; // Select third camera
camera.IsOn = true;
```

### 5. Camera Information Class

```csharp
public class CameraInfo
{
    public string Id { get; set; }           // Platform-specific camera ID
    public string Name { get; set; }         // Human-readable name ("Back Camera", "Front Camera")
    public CameraPosition Position { get; set; } // Front/Back/Unknown
    public int Index { get; set; }           // Index for manual selection
    public bool HasFlash { get; set; }       // Flash capability
}
```

### 6. Photo Capture

#### Basic Capture
```csharp
// Event-based approach (recommended)
camera.CaptureSuccess += OnCaptureSuccess;
camera.CaptureFailed += OnCaptureFailed;

private async void TakePicture()
{
    if (camera.State == CameraState.On && !camera.IsBusy)
    {
        // Optional: Flash screen effect
        camera.FlashScreen(Color.Parse("#EEFFFFFF"));

        // Capture photo
        await camera.TakePicture().ConfigureAwait(false);
    }
}

private void OnCaptureSuccess(object sender, CapturedImage captured)
{
    // Handle successful capture
    var image = captured.Image; // SKImage
    var timestamp = captured.Time;
    var metadata = captured.Metadata;

    // Process or save image
    ProcessCapturedImage(image);
}

private void OnCaptureFailed(object sender, Exception ex)
{
    // Handle capture failure
    Debug.WriteLine($"Capture failed: {ex.Message}");
}
```

#### Command-Based Capture (MVVM)
```csharp
public ICommand CommandCapturePhoto => new Command(async () =>
{
    if (camera.State == CameraState.On && !camera.IsBusy)
    {
        camera.FlashScreen(Color.Parse("#EEFFFFFF"));
        await camera.TakePicture().ConfigureAwait(false);
    }
});
```

### 7. Real-Time Effects

```csharp
// Cycle through available effects
private void CycleEffects()
{
    var effects = new[]
    {
        SkiaImageEffect.None,
        SkiaImageEffect.Sepia,
        SkiaImageEffect.BlackAndWhite,
        SkiaImageEffect.Pastel
    };

    var currentIndex = Array.IndexOf(effects, camera.Effect);
    var nextIndex = (currentIndex + 1) % effects.Length;
    camera.Effect = effects[nextIndex];
}
```

### 8. Zoom Control

```csharp
// Manual zoom
camera.Zoom = 2.0; // 2x zoom

// Zoom with limits
private void ZoomIn()
{
    camera.Zoom = Math.Min(camera.Zoom + 0.2, camera.ZoomLimitMax);
}

private void ZoomOut()
{
    camera.Zoom = Math.Max(camera.Zoom - 0.2, camera.ZoomLimitMin);
}

// Pinch-to-zoom gesture (XAML)
// <draw:SkiaHotspotZoom ZoomMax="3" ZoomMin="1" Zoomed="OnZoomed" />

private void OnZoomed(object sender, ZoomEventArgs e)
{
    camera.Zoom = e.Value;
}
```

### 9. Camera State Management

```csharp
// Subscribe to state changes
camera.StateChanged += OnCameraStateChanged;

private void OnCameraStateChanged(object sender, CameraState newState)
{
    switch (newState)
    {
        case CameraState.Off:
            // Camera is off
            break;
        case CameraState.On:
            // Camera is running
            break;
        case CameraState.Error:
            // Camera error occurred
            break;
    }
}

// Check camera state before operations
if (camera.State == CameraState.On)
{
    // Safe to perform camera operations
}
```

### 10. Preview Frame Processing

```csharp
// Subscribe to preview frames for AI/ML processing
camera.NewPreviewSet += OnNewPreviewFrame;

private void OnNewPreviewFrame(object sender, LoadedImageSource source)
{
    // Process preview frame for AI/ML
    Task.Run(() => ProcessFrameForAI(source));
}

private void ProcessFrameForAI(LoadedImageSource source)
{
    // Your AI/ML processing here
    // Note: This runs for every frame, optimize carefully
}
```

### 11. Permission Handling

```csharp
// Check and request permissions before starting camera
SkiaCamera.CheckPermissions(async (granted) =>
{
    if (granted)
    {
        camera.IsOn = true;
    }
    else
    {
        // Handle permission denied
        await DisplayAlert("Permission Required", "Camera access is required", "OK");
    }
});
```

### 12. Complete MVVM Example

#### ViewModel
```csharp
public class CameraViewModel : INotifyPropertyChanged, IDisposable
{
    private SkiaCamera _camera;

    public void AttachCamera(SkiaCamera camera)
    {
        if (_camera == null && camera != null)
        {
            _camera = camera;
            _camera.CaptureSuccess += OnCaptureSuccess;
            _camera.StateChanged += OnCameraStateChanged;
            _camera.NewPreviewSet += OnNewPreviewSet;
        }
    }

    public ICommand CommandCapturePhoto => new Command(async () =>
    {
        if (_camera?.State == CameraState.On && !_camera.IsBusy)
        {
            _camera.FlashScreen(Color.Parse("#EEFFFFFF"));
            await _camera.TakePicture().ConfigureAwait(false);
        }
    });

    public ICommand CommandSwitchCamera => new Command(() =>
    {
        if (_camera?.IsOn == true)
        {
            _camera.Facing = _camera.Facing == CameraPosition.Selfie
                ? CameraPosition.Default
                : CameraPosition.Selfie;
        }
    });

    private void OnCaptureSuccess(object sender, CapturedImage captured)
    {
        // Handle captured image
        MainThread.BeginInvokeOnMainThread(() =>
        {
            // Update UI with captured image
        });
    }

    public void Dispose()
    {
        if (_camera != null)
        {
            _camera.CaptureSuccess -= OnCaptureSuccess;
            _camera.StateChanged -= OnCameraStateChanged;
            _camera.NewPreviewSet -= OnNewPreviewSet;
            _camera = null;
        }
    }
}
```

#### Page Code-Behind
```csharp
public partial class CameraPage : ContentPage
{
    private readonly CameraViewModel _viewModel;

    public CameraPage(CameraViewModel viewModel)
    {
        _viewModel = viewModel;
        BindingContext = _viewModel;
        InitializeComponent();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();

        // Attach camera to viewmodel
        var camera = this.FindByName<SkiaCamera>("CameraControl");
        _viewModel.AttachCamera(camera);

        // Start camera with permission check
        SkiaCamera.CheckPermissions(async (granted) =>
        {
            if (granted)
            {
                camera.IsOn = true;
            }
        });
    }
}
```

## Testing & Examples

Real-world examples are available in the demo project:
- `ScreenCameraPhoto.xaml` - Complete camera UI with controls
- `ScreenCameraPhoto.xaml.cs` - Event handling and camera operations
- `TakePictureViewModel.cs` - MVVM pattern implementation
- `CameraLayout.cs` - Custom camera container with lifecycle management

## Advanced Patterns & Best Practices

### 1. Camera Selection UI

```csharp
public async Task ShowCameraPicker()
{
    var cameras = await camera.GetAvailableCamerasAsync();

    // Create picker with detailed camera info
    var options = cameras.Select(c =>
        $"{c.Name} ({c.Position}){(c.HasFlash ? " 📸" : "")}"
    ).ToArray();

    var selectedIndex = await DisplayActionSheet("Select Camera", "Cancel", null, options);

    if (selectedIndex >= 0)
    {
        camera.Facing = CameraPosition.Manual;
        camera.CameraIndex = selectedIndex;
        camera.IsOn = true;
    }
}
```

### 2. Multi-Camera Device Support

```csharp
// Detect and utilize multiple cameras
var cameras = await camera.GetAvailableCamerasAsync();

// Find specific camera types
var ultraWide = cameras.FirstOrDefault(c => c.Name.Contains("Ultra"));
var telephoto = cameras.FirstOrDefault(c => c.Name.Contains("Telephoto"));
var mainCamera = cameras.FirstOrDefault(c => c.Position == CameraPosition.Default);

// Smart camera selection based on use case
public void SelectBestCameraForScenario(string scenario)
{
    switch (scenario.ToLower())
    {
        case "landscape":
            // Use ultra-wide if available
            if (ultraWide != null)
            {
                camera.Facing = CameraPosition.Manual;
                camera.CameraIndex = ultraWide.Index;
            }
            break;

        case "portrait":
            // Use telephoto for better portraits
            if (telephoto != null)
            {
                camera.Facing = CameraPosition.Manual;
                camera.CameraIndex = telephoto.Index;
            }
            break;

        default:
            // Use main camera
            camera.Facing = CameraPosition.Default;
            break;
    }
}
```

### 3. Performance Optimization

```csharp
// Cache camera enumeration
private static List<CameraInfo> _cachedCameras;

public async Task<List<CameraInfo>> GetCamerasOptimized()
{
    if (_cachedCameras == null)
    {
        _cachedCameras = await camera.GetAvailableCamerasAsync();
    }
    return _cachedCameras;
}

// Efficient preview processing
private readonly SemaphoreSlim _frameProcessingSemaphore = new(1, 1);

private void OnNewPreviewFrame(object sender, LoadedImageSource source)
{
    // Skip frame if still processing previous one
    if (!_frameProcessingSemaphore.Wait(0))
        return;

    Task.Run(async () =>
    {
        try
        {
            await ProcessFrameAsync(source);
        }
        finally
        {
            _frameProcessingSemaphore.Release();
        }
    });
}
```

### 4. Error Handling & Recovery

```csharp
private int _cameraRestartAttempts = 0;
private const int MaxRestartAttempts = 3;

private async void OnCameraError(object sender, string error)
{
    Debug.WriteLine($"Camera error: {error}");

    if (_cameraRestartAttempts < MaxRestartAttempts)
    {
        _cameraRestartAttempts++;

        // Wait before retry
        await Task.Delay(1000);

        // Attempt restart
        camera.IsOn = false;
        await Task.Delay(500);
        camera.IsOn = true;
    }
    else
    {
        // Show user-friendly error
        await DisplayAlert("Camera Error",
            "Camera is not responding. Please restart the app.", "OK");
    }
}

private void OnCameraStateChanged(object sender, CameraState newState)
{
    if (newState == CameraState.On)
    {
        // Reset retry counter on successful start
        _cameraRestartAttempts = 0;
    }
}
```

### 5. Memory Management

```csharp
public class CameraManager : IDisposable
{
    private SkiaCamera _camera;
    private bool _disposed = false;

    public void AttachCamera(SkiaCamera camera)
    {
        DetachCamera(); // Clean up previous camera

        _camera = camera;
        _camera.CaptureSuccess += OnCaptureSuccess;
        _camera.CaptureFailed += OnCaptureFailed;
        _camera.StateChanged += OnStateChanged;
    }

    private void DetachCamera()
    {
        if (_camera != null)
        {
            _camera.CaptureSuccess -= OnCaptureSuccess;
            _camera.CaptureFailed -= OnCaptureFailed;
            _camera.StateChanged -= OnStateChanged;
            _camera.IsOn = false;
            _camera = null;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            DetachCamera();
            _disposed = true;
        }
    }
}
```

### 6. Custom Camera Effects Pipeline

```csharp
public class CameraEffectsManager
{
    private readonly SkiaCamera _camera;
    private readonly Queue<SkiaImageEffect> _effectQueue = new();

    public CameraEffectsManager(SkiaCamera camera)
    {
        _camera = camera;
    }

    public void QueueEffect(SkiaImageEffect effect, TimeSpan duration)
    {
        _effectQueue.Enqueue(effect);
        ProcessEffectQueue(duration);
    }

    private async void ProcessEffectQueue(TimeSpan duration)
    {
        while (_effectQueue.Count > 0)
        {
            var effect = _effectQueue.Dequeue();
            _camera.Effect = effect;
            await Task.Delay(duration);
        }

        // Reset to no effect
        _camera.Effect = SkiaImageEffect.None;
    }

    public void CreatePhotoWithEffect(SkiaImageEffect effect)
    {
        var originalEffect = _camera.Effect;

        // Apply effect temporarily
        _camera.Effect = effect;

        // Take photo
        _camera.TakePicture().ContinueWith(_ =>
        {
            // Restore original effect
            MainThread.BeginInvokeOnMainThread(() =>
            {
                _camera.Effect = originalEffect;
            });
        });
    }
}
```

## API Reference

### Core Properties
```csharp
// Camera Control
public bool IsOn { get; set; }                    // Start/stop camera
public CameraPosition Facing { get; set; }        // Camera selection mode
public int CameraIndex { get; set; }              // Manual camera index
public CameraState State { get; }                 // Current state (read-only)
public bool IsBusy { get; }                       // Processing state (read-only)

// Capture Settings
public CaptureQuality CapturePhotoQuality { get; set; } // Photo quality
public SkiaImageEffect Effect { get; set; }       // Real-time effects

// Zoom & Limits
public double Zoom { get; set; }                  // Current zoom level
public double ZoomLimitMin { get; set; }          // Minimum zoom
public double ZoomLimitMax { get; set; }          // Maximum zoom
```

### Core Methods
```csharp
// Camera Management
public async Task<List<CameraInfo>> GetAvailableCamerasAsync()
public async Task<List<CameraInfo>> RefreshAvailableCamerasAsync()
public static void CheckPermissions(Action<bool> callback)

// Capture Operations
public async Task TakePicture()
public void FlashScreen(Color color, long duration = 250)

// Camera Controls
public void TurnOnFlash()
public void TurnOffFlash()
public void SetZoom(double value)
```

### Events
```csharp
public event EventHandler<CapturedImage> CaptureSuccess;
public event EventHandler<Exception> CaptureFailed;
public event EventHandler<LoadedImageSource> NewPreviewSet;
public event EventHandler<CameraState> StateChanged;
public event EventHandler<string> OnError;
public event EventHandler<double> Zoomed;
```

### Enums
```csharp
public enum CameraPosition { Default, Selfie, Manual }
public enum CameraState { Off, On, Error }
public enum CaptureQuality { Max, Medium, Low, Preview }
public enum SkiaImageEffect { None, Sepia, BlackAndWhite, Pastel }
```

## Troubleshooting

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| **Camera not starting** | Missing permissions | Use `SkiaCamera.CheckPermissions()` |
| **Black preview** | Camera enumeration failed | Check device camera availability |
| **Capture failures** | Storage permissions | Verify write permissions |
| **Performance issues** | Unoptimized preview processing | Cache controls, limit frame processing |
| **Manual selection fails** | Invalid `CameraIndex` | Verify index is 0 to `cameras.Count-1` |
| **App crashes on camera switch** | Rapid camera changes | Add delays between camera operations |
| **Memory leaks** | Event handlers not removed | Properly dispose and unsubscribe events |

### Debug Tips

```csharp
// Enable debug logging
#if DEBUG
camera.OnError += (s, error) => Debug.WriteLine($"Camera Error: {error}");
camera.StateChanged += (s, state) => Debug.WriteLine($"Camera State: {state}");
#endif

// Check camera availability
var cameras = await camera.GetAvailableCamerasAsync();
Debug.WriteLine($"Available cameras: {cameras.Count}");
foreach (var cam in cameras)
{
    Debug.WriteLine($"  {cam.Index}: {cam.Name} ({cam.Position}) Flash: {cam.HasFlash}");
}

// Monitor performance
var stopwatch = Stopwatch.StartNew();
await camera.TakePicture();
Debug.WriteLine($"Capture took: {stopwatch.ElapsedMilliseconds}ms");
```

### Platform-Specific Notes

#### Android
- Requires Camera2 API (API level 21+)
- Some devices may have camera enumeration delays
- Test on various Android versions and manufacturers

#### iOS/macOS
- AVFoundation framework required
- Camera permissions must be declared in Info.plist
- Some camera types only available on newer devices

#### Windows
- UWP/WinUI MediaCapture APIs
- Camera access requires capability declaration
- Desktop apps may need additional permissions

## AI Agent Integration Guide

This section provides specific guidance for AI agents working with SkiaCamera.

### Quick Start for AI Agents

```csharp
// 1. Basic camera setup
var camera = new SkiaCamera
{
    Facing = CameraPosition.Default,
    CapturePhotoQuality = CaptureQuality.Medium,
    IsOn = true
};

// 2. Event handling
camera.CaptureSuccess += (s, captured) => {
    // Process captured.Image (SKImage)
    // Access captured.Metadata for EXIF data
};

// 3. Manual camera selection
var cameras = await camera.GetAvailableCamerasAsync();
camera.Facing = CameraPosition.Manual;
camera.CameraIndex = 2; // Select third camera

// 4. Take photo
await camera.TakePicture();
```

### Key Patterns for AI Agents

1. **Always check `camera.State == CameraState.On` before operations**
2. **Use `camera.IsOn = true/false` for lifecycle management**
3. **Subscribe to events before starting camera**
4. **Handle permissions with `SkiaCamera.CheckPermissions()`**
5. **Use `ConfigureAwait(false)` for async operations**

### Common AI Agent Mistakes to Avoid

❌ **Don't do this:**
```csharp
camera.Start(); // Direct method call
camera.TakePicture(); // Without checking state
```

✅ **Do this instead:**
```csharp
camera.IsOn = true; // Proper lifecycle
if (camera.State == CameraState.On && !camera.IsBusy)
    await camera.TakePicture().ConfigureAwait(false);
```

## Features

### ✅ Completed Features
- [x] **Cross-platform support** (Android, iOS, macOS, Windows)
- [x] **Hardware-accelerated rendering** with SkiaSharp
- [x] **Manual camera selection** by index with enumeration
- [x] **Automatic camera selection** (front/back)
- [x] **Real-time preview effects** (Sepia, B&W, Pastel)
- [x] **Photo capture** with metadata and quality settings
- [x] **Zoom control** with gesture support
- [x] **Flash control** and screen flash effects
- [x] **Event-driven architecture** for MVVM patterns
- [x] **Permission handling** with built-in checks
- [x] **State management** with proper lifecycle
- [x] **Performance optimization** with caching and GPU acceleration

### 🚧 Future Enhancements
- [ ] **Video recording** with quality settings
- [ ] **Advanced camera controls** (focus, exposure, ISO, white balance)
- [ ] **Custom resolution selection** for capture and preview
- [ ] **Camera capability detection** (zoom ranges, supported formats)
- [ ] **HDR capture** support
- [ ] **Burst mode** for rapid captures
- [ ] **QR/Barcode scanning** integration

## References:

iOS: 
* [Manual Camera Controls in Xamarin.iOS](https://github.com/MicrosoftDocs/xamarin-docs/blob/0506e3bf14b520776fc7d33781f89069bbc57138/docs/ios/user-interface/controls/intro-to-manual-camera-controls.md) by David Britch

