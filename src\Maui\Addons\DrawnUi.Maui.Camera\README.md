﻿# SkiaCamera

A totally drawn camera control for .NET MAUI with manual camera selection support.
Supports Android, iOS, MacCatalyst, Windows.

* Provides preview frames and still capture results for further processing.
Easily pass images to AI/ML.

* Renders live preview on a SkiaSharp canvas with hardware acceleration.
Apply shaders, adjustments and transforms to camera preview in realtime and draw anything over.

* **NEW**: Manual camera selection - choose any available camera by index, perfect for devices with multiple cameras.

## Platform Support

| Platform | Status | Implementation |
|----------|--------|----------------|
| Android | ✅ Complete | Camera2 API with CameraX |
| iOS | ✅ Complete | AVFoundation (shared with macCatalyst) |
| MacCatalyst | ✅ Complete | AVFoundation (shared with iOS) |
| Windows | ✅ Complete | MediaCapture with WinRT APIs |

## Installation

```
//todo add drawnui.maui.camera nuget etc
```

## Set up permissions

### Windows:

No specific setup needed.

### Apple:

Put this inside the file `Platforms/iOS/Info.plist` and `Platforms/MacCatalyst/Info.plist` inside the `<dict>` tag:

```xml
  <key>NSCameraUsageDescription</key>
  <string>Allow access to the camera</string>	
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow access to the library to save photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to the library to save photos</string>
```

If you want to geo-tag photos (get and save GPS location metadata) add this:

```xml
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To be able to geotag photos</string>
```

### Android

Put this inside the file `Platforms/Android/AndroidManifest.xml` inside the `<manifest>` tag:

```xml
    <!--for camera and gallery access-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.CAMERA" />
```

If you want to geo-tag photos (get and save GPS location metadata) add this:

```xml
  <!--geotag photos-->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

## Basic Usage Example

### Automatic Camera Selection (Default Behavior)

```csharp
// Create camera with automatic selection
var camera = new SkiaCamera
{
    Facing = CameraPosition.Default, // Back camera
    IsOn = true
};

// Or front camera
var frontCamera = new SkiaCamera
{
    Facing = CameraPosition.Selfie, // Front camera
    IsOn = true
};
```

### Manual Camera Selection (NEW)

```csharp
// Get available cameras
var camera = new SkiaCamera();
var availableCameras = await camera.GetAvailableCamerasAsync();

// List all cameras
foreach (var cam in availableCameras)
{
    Console.WriteLine($"Camera {cam.Index}: {cam.Name} ({cam.Position})");
}

// Select specific camera by index (e.g., third camera)
camera.Facing = CameraPosition.Manual;
camera.CameraIndex = 2; // 0-based index
camera.IsOn = true;
```

### Camera Selection Properties

| Property | Type | Description |
|----------|------|-------------|
| `Facing` | `CameraPosition` | `Default` (back), `Selfie` (front), or `Manual` |
| `CameraIndex` | `int` | Camera index for manual selection (default: -1) |

When `CameraIndex = -1`: Uses automatic selection based on `Facing`
When `Facing = Manual`: Uses camera at specified `CameraIndex`

### Available Camera Information

```csharp
public class CameraInfo
{
    public string Id { get; set; }           // Platform-specific camera ID
    public string Name { get; set; }         // Human-readable name
    public CameraPosition Position { get; set; } // Front/Back/Unknown
    public int Index { get; set; }           // Index for manual selection
    public bool HasFlash { get; set; }       // Flash support
}
```

It's important to understand the difference between setting the `IsOn` property to `true` and invoking the `Start` method directly.

In some rare scenarios your app could have several camera controls and `IsOn` is like a radiobutton value, meaning "this camera is `On` right now", others must have this value set to `false`.
When app goes to background all cameras stop and when app returns from background state the control would need to know whether to automatically resume (start again basically) a specific camera instance, and it would if `IsOn` was set to `true`. 

Also when your control appears for the first time and you didn't get user camera permissions yet the native platform camera control should not be created yet, and it wouldn't if `SkiaControl` `IsOn` is still false. So the main workflow here is to create `SkiaCamera`, execute your needed preliminary logic and then safely turn the camera on by setting `IsOn` to `true`. For example, you can invoke `public ICommand CommandCheckPermissionsAndStart` it does something similar, like one could guess by it's name.

## Testing

A test page is provided in the Sandbox project:
- `CameraTestPage.xaml` - UI layout with camera preview and controls
- `CameraTestPage.xaml.cs` - Event handling and camera operations

## Common Patterns

### Camera Selection UI

```csharp
public async Task ShowCameraPicker()
{
    var camera = new SkiaCamera();
    var cameras = await camera.GetAvailableCamerasAsync();

    // Create picker options
    var options = cameras.Select(c => $"{c.Name} ({c.Position})").ToArray();

    // Show picker (using your preferred UI method)
    var selectedIndex = await DisplayActionSheet("Select Camera", "Cancel", null, options);

    if (selectedIndex >= 0)
    {
        camera.Facing = CameraPosition.Manual;
        camera.CameraIndex = selectedIndex;
        camera.IsOn = true;
    }
}
```

### Multi-Camera Device Support

```csharp
// Perfect for devices with 3+ cameras
var cameras = await camera.GetAvailableCamerasAsync();
if (cameras.Count >= 3)
{
    // Select the third camera (ultra-wide, telephoto, etc.)
    camera.Facing = CameraPosition.Manual;
    camera.CameraIndex = 2;
    camera.IsOn = true;
}
```

### Image effects and filters

### Consume preview for AI/ML processing



## Troubleshooting

1. **Camera not starting**: Check permissions and camera availability
2. **Black preview**: Verify camera device enumeration
3. **Capture failures**: Check storage permissions and available space
4. **Performance issues**: Cache controls that are drawn over. Ensure logs not running for every frame.
5. **Manual selection not working**: Verify `CameraIndex` is valid (0 to cameras.Count-1)
6. **Invalid camera index**: Falls back to first available camera automatically

## Features

### ✅ Completed
- [x] Cross-platform camera preview and capture
- [x] Hardware-accelerated SkiaSharp rendering
- [x] Manual camera selection by index
- [x] Camera enumeration and information
- [x] Automatic camera selection (front/back)
- [x] Real-time preview effects and filters
- [x] Still image capture with metadata

### 🚧 Future Enhancements
- [ ] Video recording support
- [ ] Advanced camera controls (focus, exposure, ISO)
- [ ] Custom resolution selection
- [ ] Camera capability detection (zoom ranges, etc.)

## References:

iOS: 
* [Manual Camera Controls in Xamarin.iOS](https://github.com/MicrosoftDocs/xamarin-docs/blob/0506e3bf14b520776fc7d33781f89069bbc57138/docs/ios/user-interface/controls/intro-to-manual-camera-controls.md) by David Britch

