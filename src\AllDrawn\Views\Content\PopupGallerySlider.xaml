﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.PopupGallerySlider"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:interfaces="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Interfaces"
    x:Name="ThisControl"
    draw:AddGestures.CommandTapped="{Binding CommandCloseGallery}"
    x:DataType="interfaces:IFullscreenGalleryManager"
    HorizontalOptions="Fill"
    BackgroundColor="#66000000"
    VerticalOptions="Fill">

    <draw:SkiaCarousel
        x:Name="MainCarousel"
        Bounces="True"
        HorizontalOptions="Fill"
        ItemsSource="{Binding GalleryItems}"
        SelectedIndex="{Binding SelectedGalleryIndex}"
        SidesOffset="0"
        Spacing="16"
        Tag="Carousel"
        VerticalOptions="Fill">

        <draw:SkiaLayout.ItemTemplate>
            <DataTemplate>

                <draw:ZoomContent
                    PanningMode="OneFinger"
                    UseCache="GPU"
                    ZoomMax="2"
                    ZoomMin="1">

                    <draw:SkiaLayout
                        x:DataType="x:String"
                        BackgroundColor="Transparent"
                        HorizontalOptions="Fill"
                        Tag="Tpl"
                        UseCache="Operations"
                        VerticalOptions="Fill">

                        <draw:SkiaSvg
                            HeightRequest="110"
                            HorizontalOptions="Center"
                            LockRatio="1"
                            Opacity="0.5"
                            TintColor="{StaticResource Gray950}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            ZIndex="-1">
                            <![CDATA[ 
                            <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15 0H5C2.243 0 0 2.243 0 5V13C0 15.757 2.243 18 5 18H15C17.757 18 20 15.757 20 13V5C20 2.243 17.757 0 15 0ZM5 2H15C16.654 2 18 3.346 18 5V9.58594L14.417 6.00293C13.636 5.22193 12.364 5.22193 11.583 6.00293L7 10.5859L6.41701 10.0029C5.63601 9.22193 4.36399 9.22193 3.58299 10.0029L2 11.5859V5C2 3.346 3.346 2 5 2ZM4.5 6C4.5 5.172 5.172 4.5 6 4.5C6.828 4.5 7.5 5.172 7.5 6C7.5 6.828 6.828 7.5 6 7.5C5.172 7.5 4.5 6.828 4.5 6Z" fill="#41416E"/>
                            </svg>
                            ]]>
                        </draw:SkiaSvg>

                        <draw:SkiaImage
                            Margin="0"
                            Aspect="AspectFit"
                            EraseChangedContent="True"
                            HorizontalOptions="Fill"
                            LoadSourceOnFirstDraw="False"
                            Source="{Binding .}"
                            VerticalOptions="Fill" />

                    </draw:SkiaLayout>

                </draw:ZoomContent>

            </DataTemplate>

        </draw:SkiaLayout.ItemTemplate>

    </draw:SkiaCarousel>

    <draw:SkiaMarkdownLabel
        Margin="24"
        Padding="8"
        BackgroundColor="#66000000"
        HorizontalOptions="Fill"
        HorizontalTextAlignment="Center"
        Style="{StaticResource MarkdownLabelDefaultStyle}"
        Text="Using a ***SkiaCarousel*** inside a ***SkiaShell*** popup. Showing a ***SkiaImage*** inside, a powerful control that can apply effects and provide responsive aspects like _AspectCover_ etc."
        UseCache="Image"
        VerticalOptions="End" />


</draw:SkiaLayout>
