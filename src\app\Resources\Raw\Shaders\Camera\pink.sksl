﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the neo-noir pink and blue aesthetic from Drive
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Drive movie styled color</returns>
half3 driveGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Create masks for different tonal ranges
    float highlightMask = smoothstep(0.6, 1.0, luminance);
    float midtoneMask = smoothstep(0.2, 0.6, luminance) * (1.0 - highlightMask);
    float shadowMask = 1.0 - smoothstep(0.0, 0.4, luminance);
    
    // Pink/magenta for highlights and warm areas
    half3 pinkTone = half3(1.3, 0.7, 1.1);
    
    // Cool blue for shadows
    half3 blueTone = half3(0.6, 0.8, 1.4);
    
    // Neutral slightly warm for midtones
    half3 neutralTone = half3(1.1, 1.0, 0.95);
    
    // Blend the tones
    half3 gradedColor = color * (pinkTone * highlightMask + 
                                neutralTone * midtoneMask + 
                                blueTone * shadowMask);
    
    // Preserve luminance
    float newLuminance = dot(gradedColor, half3(0.299, 0.587, 0.114));
    gradedColor *= luminance / max(newLuminance, 0.001);
    
    // Boost contrast for that stylized look
    gradedColor = (gradedColor - 0.5) * 1.2 + 0.5;
    
    // Slight overall pink cast
    gradedColor.r *= 1.05;
    gradedColor.b *= 1.02;
    
    // Increase saturation
    half3 finalColor = mix(half3(luminance), gradedColor, 1.4);
    
    return clamp(finalColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 gradedColor = driveGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}