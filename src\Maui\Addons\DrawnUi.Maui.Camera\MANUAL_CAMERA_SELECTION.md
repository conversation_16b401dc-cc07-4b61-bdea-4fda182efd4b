# Manual Camera Selection Enhancement

This document describes the new manual camera selection functionality added to SkiaCamera.

## Overview

The SkiaCamera control now supports manual camera selection in addition to the existing automatic selection based on camera facing (Default/Selfie). This allows users to select any available camera on the device by index.

## New Features

### 1. Extended CameraPosition Enum

```csharp
public enum CameraPosition
{
    Default,  // Back camera (existing)
    Selfie,   // Front camera (existing)
    Manual    // Manual selection by index (new)
}
```

### 2. New Properties

#### CameraIndex Property
```csharp
public int CameraIndex { get; set; } = -1;
```
- When set to -1 (default): Uses automatic selection based on `Facing` property
- When `Facing` is set to `Manual`: This property determines which camera to use (0-based index)
- If the index is invalid, falls back to the first available camera

### 3. New Methods

#### GetAvailableCamerasAsync()
```csharp
public virtual async Task<List<CameraInfo>> GetAvailableCamerasAsync()
```
Returns a cached list of available cameras.

#### RefreshAvailableCamerasAsync()
```csharp
public virtual async Task<List<CameraInfo>> RefreshAvailableCamerasAsync()
```
Refreshes and returns the list of available cameras.

### 4. CameraInfo Class

```csharp
public class CameraInfo
{
    public string Id { get; set; }           // Camera device identifier
    public string Name { get; set; }         // Human-readable camera name
    public CameraPosition Position { get; set; } // Front/Back/Unknown
    public int Index { get; set; }           // Index in available cameras list
    public bool HasFlash { get; set; }       // Whether camera supports flash
}
```

## Usage Examples

### Example 1: List Available Cameras

```csharp
var camera = new SkiaCamera();
var availableCameras = await camera.GetAvailableCamerasAsync();

foreach (var cameraInfo in availableCameras)
{
    Console.WriteLine($"Camera {cameraInfo.Index}: {cameraInfo.Name} ({cameraInfo.Position})");
}
```

### Example 2: Manual Camera Selection

```csharp
var camera = new SkiaCamera();

// Get available cameras
var cameras = await camera.GetAvailableCamerasAsync();

// Select the third camera (index 2) manually
if (cameras.Count > 2)
{
    camera.Facing = CameraPosition.Manual;
    camera.CameraIndex = 2;
    camera.IsOn = true; // Start the camera
}
```

### Example 3: Camera Picker UI

```csharp
public async Task ShowCameraPicker()
{
    var camera = new SkiaCamera();
    var cameras = await camera.GetAvailableCamerasAsync();
    
    // Create picker options
    var options = cameras.Select(c => $"{c.Name} ({c.Position})").ToArray();
    
    // Show picker (pseudo-code)
    var selectedIndex = await DisplayActionSheet("Select Camera", "Cancel", null, options);
    
    if (selectedIndex >= 0)
    {
        camera.Facing = CameraPosition.Manual;
        camera.CameraIndex = selectedIndex;
        camera.IsOn = true;
    }
}
```

## Platform Support

This functionality is supported on:
- ✅ Windows (UWP/WinUI)
- ✅ Android (Camera2 API)
- ✅ iOS/macOS (AVFoundation)

## Migration Guide

### Existing Code
No changes required for existing code. The new functionality is backward compatible:

```csharp
// This continues to work as before
var camera = new SkiaCamera
{
    Facing = CameraPosition.Default, // or CameraPosition.Selfie
    IsOn = true
};
```

### New Manual Selection
```csharp
// New manual selection capability
var camera = new SkiaCamera
{
    Facing = CameraPosition.Manual,
    CameraIndex = 2, // Select third camera
    IsOn = true
};
```

## Implementation Notes

- Camera enumeration is cached for performance
- Invalid camera indices fall back to the first available camera
- Camera restart is triggered when `Facing` or `CameraIndex` properties change
- Platform-specific camera information is exposed through the `CameraInfo` class
