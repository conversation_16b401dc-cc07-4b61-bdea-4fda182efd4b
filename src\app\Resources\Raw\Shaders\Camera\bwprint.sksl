﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Calculates Bayer dithering threshold for given pixel coordinates
/// </summary>
float get_bayer_threshold(int2 pixel_coords) {
    int x = int(mod(float(pixel_coords.x), 4.0));
    int y = int(mod(float(pixel_coords.y), 4.0));
    int index = y * 4 + x;
    
    if (index == 0) return 0.0 / 16.0;
    if (index == 1) return 8.0 / 16.0;
    if (index == 2) return 2.0 / 16.0;
    if (index == 3) return 10.0 / 16.0;
    if (index == 4) return 12.0 / 16.0;
    if (index == 5) return 4.0 / 16.0;
    if (index == 6) return 14.0 / 16.0;
    if (index == 7) return 6.0 / 16.0;
    if (index == 8) return 3.0 / 16.0;
    if (index == 9) return 11.0 / 16.0;
    if (index == 10) return 1.0 / 16.0;
    if (index == 11) return 9.0 / 16.0;
    if (index == 12) return 15.0 / 16.0;
    if (index == 13) return 7.0 / 16.0;
    if (index == 14) return 13.0 / 16.0;
    return 5.0 / 16.0;
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half3 color = iImage1.eval(inputCoord).rgb;
    
    // Convert to grayscale first (newspaper style)
    float gray = dot(color, half3(0.299, 0.587, 0.114));
    
    // Add contrast for that dramatic newspaper look
    float contrast = 1.8;  // Boost contrast significantly
    gray = clamp((gray - 0.5) * contrast + 0.5, 0.0, 1.0);
    
    // Constants for authentic old newspaper look
    int palette_size = 4;  // Just 4 levels: black, dark gray, light gray, white
    float dither_strength = 0.8;  // Strong dither for that rough print quality
    
    int2 pixel_coords = int2(fragCoord.x, fragCoord.y);
    float threshold = get_bayer_threshold(pixel_coords);
    
    // Apply dither to grayscale value
    float dither = (threshold - 0.5) * 2.0 * dither_strength / float(palette_size);
    float dithered_gray = clamp(gray + dither, 0.0, 1.0);
    
    // Find closest gray level (4 levels: 0, 1/3, 2/3, 1)
    float min_dist = 999.0;
    float closest_gray = 0.0;
    
    for (int i = 0; i < 4; i++) {
        float pal_gray = float(i) / 3.0;  // 0, 0.33, 0.67, 1.0
        float dist = abs(dithered_gray - pal_gray);
        
        if (dist < min_dist) {
            min_dist = dist;
            closest_gray = pal_gray;
        }
    }
    
    return half4(closest_gray, closest_gray, closest_gray, 1.0);
}