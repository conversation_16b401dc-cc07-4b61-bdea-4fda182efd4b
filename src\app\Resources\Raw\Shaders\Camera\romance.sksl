﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the warm, soft, dreamy look from Her (2013)
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Her movie styled color</returns>
half3 herGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Lift the overall exposure slightly for that soft overexposed feel
    half3 liftedColor = color + 0.08;
    
    // Warm color temperature shift
    liftedColor.r *= 1.15;
    liftedColor.g *= 1.05;
    liftedColor.b *= 0.90;
    
    // Soften highlights (reduce harsh contrast)
    float highlightMask = smoothstep(0.6, 1.0, luminance);
    liftedColor = mix(liftedColor, liftedColor * 0.9 + 0.1, highlightMask * 0.6);
    
    // Very gentle S-curve for soft contrast
    liftedColor = liftedColor * liftedColor * (3.0 - 2.0 * liftedColor);
    
    // Slight desaturation for that dreamy quality
    float newLuminance = dot(liftedColor, half3(0.299, 0.587, 0.114));
    half3 softColor = mix(half3(newLuminance), liftedColor, 0.9);
    
    // Add a very subtle peachy glow
    softColor.r *= 1.03;
    softColor.g *= 1.01;
    
    return clamp(softColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 gradedColor = herGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}