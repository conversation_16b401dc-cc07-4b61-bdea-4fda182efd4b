﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.ScreenLabels"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    xmlns:navigation1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    x:DataType="viewModels:SimplePageViewModel"
    BackgroundColor="Black"
    HorizontalOptions="Fill"
    Type="Wrap" 
    Split="1"
    Spacing="0"
    VerticalOptions="Fill">

    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="1"
            EndYRatio="1"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#889955</Color>
                <Color>#886655</Color>
                <Color>#222222</Color>
            </draw:SkiaGradient.Colors>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>


    <!--  STATUS BAR  -->
    <draw:SkiaShape
        BackgroundColor="Black"
        HeightRequest="{Binding Presentation.StatusBarHeightRequest}"
        HorizontalOptions="Fill"
        Tag="StatusBar" />

    <!--  NAVBAR  -->
    <navigation1:NavBarStandart />

    <!--  Simulating StackLayout inside ScrollView  -->
    <draw:SkiaScroll
        HorizontalOptions="Fill"
        
        VerticalOptions="Fill">

        <draw:SkiaLayout
            Padding="24"
            BackgroundColor="#33000000"
            HorizontalOptions="Fill"
            Type="Wrap" 
            Split="1"
            Spacing="16"
            Tag="Content"
            UseCache="Image">

            <draw:SkiaLabel
                FontFamily="FontTextTitle"
                FontSize="20"
                HorizontalTextAlignment="Start"
                Text="Drawn Ui pre-alpha"
                TextColor="{StaticResource ColorText}"
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="1. To use inside a usual Maui app, consume drawn controls here and there inside `Canvas` views."
                UseCache="None" />

            <draw:SkiaLabel
                FontFamily="FontTextTitle"
                FontSize="12"
                HorizontalTextAlignment="Start"
                Text="OR"
                TextColor="{StaticResource ColorText}"
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="2. Create a totally drawn app with just one `Canvas` as root view and consume controls inside, `SkiaShell` is provided for navigation."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="`Canvas` uses hardware acceleration and is invalidated only when needed, FPS is flex-capped. Drawn Ui uses a caching system to avoid redrawing already redrawing controls, caching as SKPicture (`Operations` cache type), SKImage (`GPU` and `Image` cache types)"
                UseCache="None" />

            <draw:SkiaLayout HorizontalOptions="Fill">

                <draw:SkiaImage
                    Aspect="AspectFit"
                    Source="Pics/use.jpg"
                    WidthRequest="190" />

                <draw:SkiaLabel
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Start"
                    AddMarginLeft="206"
                    Style="{StaticResource SkiaLabelDefaultStyle}"
                    Text="This app demonstrates the second use case."
                    UseCache="None" />

            </draw:SkiaLayout>

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="This is a `SkiaLayout` of type Column inside a `SkiaScroll`. The content layout is cached as SKImage and scrolls clipped inside the scroll viewport."
                UseCache="None" />

            <draw:SkiaLayout HorizontalOptions="Fill">

                <draw:SkiaImage
                    Aspect="AspectFit"
                    LockRatio="1"
                    Source="Pics/code.jpg"
                    WidthRequest="190" />

                <draw:SkiaLabel
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Start"
                    AddMarginLeft="206"
                    Style="{StaticResource SkiaLabelDefaultStyle}"
                    Text="All text is drawn with `SkiaLabel`, a rather powerful control with many useful properties like CharacterSpacing, ParagraphSpacing, AutoSize and much more."
                    UseCache="None">

                    <draw:SkiaLabel.FillGradient>

                        <draw:SkiaGradient
                            EndXRatio="0"
                            EndYRatio="1"
                            StartXRatio="0"
                            StartYRatio="0"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>White</Color>
                                <Color>#fffeff00</Color>
                                <Color>Red</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaLabel.FillGradient>
                </draw:SkiaLabel>
            </draw:SkiaLayout>

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - Center - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="End"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - End - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - Start - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="FillCharacters"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - FillCharacters - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="FillCharactersFull"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - FillCharactersFull - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="FillWords"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - FillWords - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />

            <draw:SkiaLabel
                HorizontalOptions="Fill"
                HorizontalTextAlignment="FillWordsFull"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="HorizontalTextAlignment - FillWordsFull - Promociones especiales y productos exclusivos. Envíos y devoluciones gratis y 60 dias de devoluciones en portátiles Surface."
                UseCache="None" />


            <draw:SkiaControl HeightRequest="{Binding Presentation.BottomTabsUnderPadding}" />

        </draw:SkiaLayout>


    </draw:SkiaScroll>


</draw:SkiaLayout>
