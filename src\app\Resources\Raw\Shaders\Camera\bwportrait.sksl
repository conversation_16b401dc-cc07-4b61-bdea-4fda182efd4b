﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Generates subtle grain for portrait work
/// </summary>
/// <param name="coord">Screen coordinate</param>
/// <returns>Subtle grain intensity</returns>
float portraitGrain(float2 coord) {
    float2 scaledCoord = coord * 0.8;
    float noise1 = fract(sin(dot(scaledCoord, float2(127.1, 311.7))) * 43758.5453);
    float noise2 = fract(sin(dot(scaledCoord, float2(269.5, 183.3))) * 17951.3421);
    return (noise1 * noise2 - 0.5) * 0.018;
}

/// <summary>
/// Applies Ilford FP4 style soft portrait B&W conversion
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Soft portrait B&W value</returns>
float fp4Conversion(half3 color) {
    // FP4 has excellent skin tone rendering, balanced sensitivity
    float luminance = dot(color, half3(0.32, 0.55, 0.13));
    
    // Gentle S-curve for smooth tonality
    luminance = sqrt(luminance);
    luminance = luminance * luminance * (3.0 - 2.0 * luminance);
    
    // Soft contrast for flattering portraits
    luminance = (luminance - 0.5) * 0.8 + 0.5;
    
    // Lift shadows slightly for detail
    luminance = luminance * 0.95 + 0.05;
    
    return clamp(luminance, 0.0, 1.0);
}

/// <summary>
/// Creates soft edge vignette for portrait feel
/// </summary>
/// <param name="uv">UV coordinates</param>
/// <returns>Soft vignette multiplier</returns>
float softVignette(float2 uv) {
    float2 centered = uv - 0.5;
    float distance = length(centered);
    return 1.0 - smoothstep(0.3, 1.0, distance * 0.8);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    float2 uv = inputCoord / iImageResolution.xy;
    
    half3 originalColor = iImage1.eval(inputCoord).rgb;
    
    float bwValue = fp4Conversion(originalColor);
    
    // Add subtle grain
    float grain = portraitGrain(inputCoord);
    bwValue += grain;
    
    // Apply soft vignette
    float vignette = softVignette(uv);
    bwValue *= vignette * 0.9 + 0.1;
    
    bwValue = clamp(bwValue, 0.0, 1.0);
    
    return half4(bwValue, bwValue, bwValue, 1.0);
}