﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the cool, desaturated blue aesthetic from <PERSON> films
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Nolan styled color</returns>
half3 nolanGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Desaturate significantly for that realistic, gritty feel
    half3 desaturatedColor = mix(color, half3(luminance), 0.4);
    
    // Cool temperature shift (signature Nolan look)
    desaturatedColor.r *= 0.85;
    desaturatedColor.g *= 0.95;
    desaturatedColor.b *= 1.25;
    
    // Crush blacks for deeper shadows
    desaturatedColor = max(desaturatedColor - 0.08, 0.0) * 1.08;
    
    // High contrast for that dramatic cinematic feel
    desaturatedColor = (desaturatedColor - 0.5) * 1.3 + 0.5;
    
    // Protect highlights from clipping
    float highlightMask = smoothstep(0.8, 1.0, luminance);
    desaturatedColor = mix(desaturatedColor, desaturatedColor * 0.85 + 0.15, highlightMask);
    
    // Additional blue tint in shadows
    float shadowMask = 1.0 - smoothstep(0.0, 0.3, luminance);
    desaturatedColor.b += shadowMask * 0.05;
    
    // Slight warm highlight to balance the cool shadows
    float warmHighlightMask = smoothstep(0.6, 1.0, luminance);
    desaturatedColor.r += warmHighlightMask * 0.03;
    desaturatedColor.g += warmHighlightMask * 0.02;
    
    return clamp(desaturatedColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    half3 gradedColor = nolanGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}