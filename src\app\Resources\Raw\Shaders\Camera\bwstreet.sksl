﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Generates heavy grain typical of pushed film
/// </summary>
/// <param name="coord">Screen coordinate</param>
/// <returns>Heavy grain intensity</returns>
float heavyGrain(float2 coord) {
    float2 grainCoord = coord * 1.2;
    float noise1 = fract(sin(dot(grainCoord, float2(127.1, 311.7))) * 43758.5453);
    float noise2 = fract(sin(dot(grainCoord * 1.3, float2(269.5, 183.3))) * 17951.3421);
    float noise3 = fract(sin(dot(grainCoord * 0.7, float2(419.2, 371.9))) * 31415.9265);
    return (noise1 + noise2 + noise3 - 1.5) * 0.04;
}

/// <summary>
/// Applies pushed Tri-X style gritty B&W conversion
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Gritty street B&W value</returns>
float pushedTriXConversion(half3 color) {
    // Pushed film has enhanced grain and contrast
    float luminance = dot(color, half3(0.4, 0.4, 0.2));
    
    // Push processing increases contrast dramatically
    luminance = pow(luminance, 0.8);
    luminance = (luminance - 0.5) * 1.8 + 0.5;
    
    // Add halation effect (light bleeding) in highlights
    float halation = smoothstep(0.7, 1.0, luminance) * 0.1;
    luminance += halation;
    
    // Crush blacks for that gritty look
    luminance = max(luminance - 0.05, 0.0) * 1.05;
    
    return clamp(luminance, 0.0, 1.0);
}

/// <summary>
/// Creates harsh vignette for urban feel
/// </summary>
/// <param name="uv">UV coordinates</param>
/// <returns>Urban vignette multiplier</returns>
float urbanVignette(float2 uv) {
    float2 centered = uv - 0.5;
    float distance = length(centered);
    return 1.0 - smoothstep(0.4, 0.9, distance * 1.2);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    float2 uv = inputCoord / iImageResolution.xy;
    
    half3 originalColor = iImage1.eval(inputCoord).rgb;
    
    float bwValue = pushedTriXConversion(originalColor);
    
    // Add heavy grain
    float grain = heavyGrain(inputCoord);
    bwValue += grain;
    
    // Apply urban vignette
    float vignette = urbanVignette(uv);
    bwValue *= vignette * 0.85 + 0.15;
    
    bwValue = clamp(bwValue, 0.0, 1.0);
    
    return half4(bwValue, bwValue, bwValue, 1.0);
}