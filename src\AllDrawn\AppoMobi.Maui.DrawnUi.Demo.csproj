<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
    <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst;</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0;</TargetFrameworks>
    <OutputType>Exe</OutputType>
		<RootNamespace>AppoMobi.Maui.DrawnUi.Demo</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
    <WarningsAsErrors>$(WarningsAsErrors);XLS0501</WarningsAsErrors>

    <UseNuget>false</UseNuget>
    
		<!-- Display name -->
		<ApplicationTitle>DrawnUi Demo</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.appomobi.drawnui.demo</ApplicationId>
		<ApplicationIdGuid>d6d6930b-b6eb-4739-8cb9-a7de08a54e22</ApplicationIdGuid>

		<!-- Versions -->
		<ApplicationVersion>2</ApplicationVersion>

		<!-- Build Time -->
		<SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>

  </PropertyGroup>

  <!--personal provisioning for iPhone-->
  <Import Project="../../../Provisioning.targets" Condition="Exists('../../../Provisioning.targets')" />

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.30" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.30" />
  </ItemGroup>

  <PropertyGroup Condition="$(TargetFramework.Contains('android')) == true">
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
  </PropertyGroup>

  <Target Condition="'$(UseNuget)' != 'true'" Name="IssueCustomWarning" BeforeTargets="BeforeBuild">
    <Warning Text="------ Building $(TargetFramework) with DrawnUi project reference ------" />
  </Target>

    
  <ItemGroup>    
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />    
  </ItemGroup>

  <ItemGroup>

    <!--Icon-->
    <MauiIcon Include="Resources\AppIcon\appicon.png" Color="#000000" BaseSize="750,750" />

    <!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

  <ItemGroup>
    <MauiFont Remove="Resources\Fonts\SeymourOne-OFL.txt" />
  </ItemGroup>

	<ItemGroup>
	  <None Remove="Platforms\Android\Resources\xml\file_paths.xml" />
	  <None Remove="Resources\Fonts\SeymourOne-Regular.ttf" />
	  <None Remove="Resources\Raw\Lottie\cross.json" />
	  <None Remove="Resources\Raw\Lottie\Loader.json" />
	  <None Remove="Resources\Raw\Lottie\LoaderSimple.json" />
	  <None Remove="Resources\Raw\Lottie\robot.json" />
	  <None Remove="Resources\Raw\Lottie\successtick.json" />
	  <None Remove="Resources\Raw\Pics\food.jpg" />
	  <None Remove="Resources\Raw\Pics\glass2.jpg" />
	  <None Remove="Resources\Raw\Pics\glass3.jpg" />
	  <None Remove="Resources\Raw\Rive\demo.riv" />
	  <None Remove="Resources\Raw\Svg\dotnet_bot.svg" />
    <None Remove="Resources\Images\" />
	  <None Remove="Views\Controls\Navigation\" />
	  <None Remove="Views\Controls\Fun\" />
  </ItemGroup>

  <!--for development-->
  <ItemGroup Condition="'$(UseNuget)' != 'true'">
    <ProjectReference Include="..\..\..\DrawnUi.Maui\src\Maui\Addons\DrawnUi.Maui.Camera\DrawnUi.Maui.Camera.csproj" />
  </ItemGroup>

  <!--production-->
  <ItemGroup Condition="'$(UseNuget)' == 'true'">
    <PackageReference Include="DrawnUi.Maui.Camera" Version="1.5.1.2" />
  </ItemGroup>

	<ItemGroup>
    <Compile Update="Resources\Strings\ResStrings.Designer.cs">
      <DependentUpon>ResStrings.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="Views\Content\ScreenAnims.xaml.cs">
      <DependentUpon>ScreenAnims.xaml</DependentUpon>
    </Compile>
    <EmbeddedResource Update="Resources\Strings\ResStrings.resx">
	    <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
	    <Generator>PublicResXFileCodeGenerator</Generator>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <MauiAsset Update="Resources\Raw\Lottie\cross.json">
	    <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
	  </MauiAsset>
	  <MauiAsset Update="Resources\Raw\Lottie\LoaderSimple.json">
	    <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
	  </MauiAsset>
	  <MauiAsset Update="Resources\Raw\Lottie\successtick.json">
	    <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
	  </MauiAsset>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Views\Controls\DrawnRadioButton.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

 
   
</Project>
