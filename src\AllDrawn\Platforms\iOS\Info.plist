<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleVersion</key>
    <string>200100</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false />
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>NSCameraUsageDescription</key>
    <string>Allow access to the camcorder for the camera</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>Allow microphone access for the camera</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>Allow access to the library to save photos</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Allow access to the library to save photos</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>To be able to geotag photos</string>
    <key>LSRequiresIPhoneOS</key>
    <true />
    <key>UIDeviceFamily</key>
    <array>
      <integer>1</integer>
      <integer>2</integer>
    </array>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>XSAppIconAssets</key>
    <string>Assets.xcassets/appicon.appiconset</string>
  </dict>
</plist>