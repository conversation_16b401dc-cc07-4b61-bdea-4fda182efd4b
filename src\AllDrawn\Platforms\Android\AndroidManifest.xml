﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.appomobi.maui.drawnui.demo" android:versionCode="110">
	<application android:allowBackup="true" 
               android:icon="@mipmap/appicon" android:supportsRtl="true" 
               android:label="Drawn UI Demo">

    <!--for camera and gallery access - file provider-->
    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}.provider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths"></meta-data>
    </provider>

  </application>
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<!--for camera and gallery access 6 lines below-->
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
	<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
	<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
	<uses-permission android:name="android.permission.CAMERA" />
	<!--<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="35" />-->

  <uses-feature
    android:name="android.hardware.camera"
    android:required="false"/>

  <queries>

    <!--Email-->
    <intent>
      <action android:name="android.intent.action.SEND" />
      <data android:mimeType="message/rfc822" />
    </intent>

    <intent>
      <action android:name="android.intent.action.SENDTO" />
      <data android:scheme="mailto" />
    </intent>

    <!-- Camera -->
    <intent>
      <action android:name="android.media.action.IMAGE_CAPTURE" />
    </intent>

  </queries>

</manifest>