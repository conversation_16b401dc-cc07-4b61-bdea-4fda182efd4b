﻿<?xml version="1.0" encoding="utf-8" ?>
<controls:TabCollectionViewDemo
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.TabScrollCells"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:content="using:AppoMobi.Maui.DrawnUi.Demo.Views"
    xmlns:controls="using:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:navigation="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:ScrollingCellsViewModel"
    BackgroundColor="WhiteSmoke"
    HorizontalOptions="Fill"
    Spacing="0"
    Tag="TabList"
    VerticalOptions="Fill">

    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="0"
            EndYRatio="1"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#FEFEFE</Color>
                <Color>#889955</Color>
                <Color>#584237</Color>
                <Color>#2D221C</Color>
            </draw:SkiaGradient.Colors>
            <draw:SkiaGradient.ColorPositions>
                <x:Double>0.0</x:Double>
                <x:Double>0.7</x:Double>
                <x:Double>0.8</x:Double>
            </draw:SkiaGradient.ColorPositions>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>


    <draw:SkiaLayout
        HorizontalOptions="Fill"
        Spacing="0"
        Tag="StackList"
        Type="Column"
        VerticalOptions="Fill">

        <!--  navbar stack  -->
        <draw:SkiaLayout
            Margin="0,0,0,-8"
            Padding="0,0,0,8"
            HorizontalOptions="Fill"
            UseCache="GPU"
            ZIndex="1">

            <draw:SkiaShape
                BackgroundColor="#6A4F42"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                ZIndex="-1">

                <draw:SkiaShape.Shadows>
                    <draw:SkiaShadow
                        Blur="3"
                        Opacity="0.6"
                        X="0"
                        Y="3"
                        Color="#111111" />
                </draw:SkiaShape.Shadows>

                <draw:SkiaControl.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="0.2"
                        StartYRatio="0.0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#889955</Color>
                            <Color>#717F46</Color>
                        </draw:SkiaGradient.Colors>
                        <draw:SkiaGradient.ColorPositions>
                            <x:Double>0.0</x:Double>
                            <x:Double>0.5</x:Double>
                        </draw:SkiaGradient.ColorPositions>
                    </draw:SkiaGradient>

                </draw:SkiaControl.FillGradient>

                <!--  <draw:SkiaImage  -->
                <!--  UseCache="Image"  -->
                <!--  Aspect="Cover"  -->
                <!--  HorizontalOptions="Fill"  -->
                <!--  InputTransparent="True"  -->
                <!--  Opacity="0.25"  -->
                <!--  Source="Pics/glass3.jpg"  -->
                <!--  VerticalOptions="Fill" />  -->

            </draw:SkiaShape>

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Spacing="0"
                Type="Column">

                <!--  STATUS BAR  -->
                <draw:SkiaControl
                    HeightRequest="{Binding Presentation.Shell.StatusBarHeight}"
                    HorizontalOptions="Fill"
                    Tag="StatusBar"
                    UseCache="Operations" />

                <!--  btns  -->
                <navigation:CustomColumnsSelector
                    ColumnDefinitions="*,*,*"
                    HeightRequest="52"
                    HorizontalOptions="Fill"
                    RowDefinitions="*"
                    SelectedIndex="{Binding Source={x:Reference StackCells}, Path=Split}"
                    Tag="Toolbar"
                    Type="Grid"
                    UseCache="Image"
                    ZIndex="1">

                    <controls:SmallButton
                        CommandTapped="{Binding CommandSetColumns}"
                        CommandTappedParameter="1"
                        HorizontalOptions="Center"
                        Text="1"
                        VerticalOptions="Center"
                        WidthRequest="36" />

                    <controls:SmallButton
                        Grid.Column="1"
                        CommandTapped="{Binding CommandSetColumns}"
                        CommandTappedParameter="2"
                        HorizontalOptions="Center"
                        Text="2"
                        VerticalOptions="Center"
                        WidthRequest="36" />

                    <controls:SmallButton
                        Grid.Column="2"
                        CommandTapped="{Binding CommandSetColumns}"
                        CommandTappedParameter="3"
                        HorizontalOptions="Center"
                        Text="3"
                        VerticalOptions="Center"
                        WidthRequest="36" />

                </navigation:CustomColumnsSelector>

            </draw:SkiaLayout>

        </draw:SkiaLayout>

        <draw:SkiaScroll
            LoadMoreCommand="{Binding CommandLoadMore}"
            LoadMoreOffset="0"
            x:Name="BannersScroll"
            HeaderBehind="True"
            HeaderParallaxRatio="0.75"
            HorizontalOptions="Fill"
            IgnoreWrongDirection="True"
            IsRefreshing="{Binding IsLoading, Mode=TwoWay}"
            MaxBounceVelocity="400"
            MaxVelocity="6000"
            ParallaxOverscrollEnabled="True"
            RefreshCommand="{Binding CommandRefreshData}"
            RefreshDistanceLimit="150"
            RefreshEnabled="True"
            RefreshShowDistance="100"
            ResetScrollPositionOnContentSizeChanged="False"
            Tag="ScrollCells"
            VerticalOptions="Fill">

            <!--  HEADER  -->
            <draw:SkiaScroll.Header>

                <content:ScrollHeader
                    Padding="0,8"
                    draw:AddGestures.AnimationTapped="Ripple"
                    draw:AddGestures.CommandTapped="{Binding CommandToggleBlur}"
                    BackgroundColor="Transparent"
                    HeightRequest="-1"
                    HorizontalOptions="Fill"
                    Tag="Header"
                    Type="Column">

                    <draw:SkiaMarkdownLabel
                        Margin="24,8"
                        FontFamily="FontText"
                        FontSize="13"
                        HorizontalOptions="Fill"
                        HorizontalTextAlignment="Center"
                        LineSpacing="1.33"
                        ParagraphSpacing="0"
                        Text="App totally __drawn on one canvas__ using _SkiaSharp_ 🎨🖌❤.&#x0a;Recycled 💯 cells are loading images from internet in realtime by 50 with LoadMore. 🔥 Tap this header to toggle blurred tabbar. 🔎"
                        TextColor="Black"
                        UseCache="GPU" />

                    <!--  HORIZONTAL SCROLL  -->
                    <draw:SkiaScroll
                        x:Name="LineScroll"
                        Bounces="True"
                        FrictionScrolled="0.3"
                        HeightRequest="144"
                        HorizontalOptions="Fill"
                        IgnoreWrongDirection="True"
                        LockChildrenGestures="PassTap"
                        Orientation="Horizontal"
                        Tag="HScroll">

                        <!--  in this screen we load 10 items and simulate a templated horizontal stackLayout, not recycled  -->
                        <draw:SkiaLayout
                            AddMarginBottom="4"
                            ItemsSource="{Binding ItemsSmall}"
                            RecyclingTemplate="Enabled"
                            Spacing="4"
                            Tag="HeaderScrollStack"
                            Type="Row"
                            VerticalOptions="Fill">

                            <draw:SkiaLayout.ItemTemplate>
                                <DataTemplate>

                                    <!--
                                        using a custom cell control for faster than bindings processing
                                        to set ui props from code-behind
                                    -->
                                    <!--  can use CellWIthHorizontalParallax with no caching then..  -->
                                    <controls:FastCellWithBanner
                                        draw:AddGestures.AnimationTapped="Ripple"
                                        x:DataType="viewModels:SimpleItemViewModel"
                                        VerticalOptions="Fill"
                                        WidthRequest="148">

                                        <!--  clipped cell content container - cache DoubleImage  -->
                                        <!--  using a custom SkiaShapeTouch to open popups from the center of a tapped cell  -->
                                        <!--  uisng ImageDoubleBuffered cache as this changes dinamically  -->
                                        <controls:SkiaShapeTouch
                                            AddMarginLeft="8"
                                            BackgroundColor="Black"
                                            CommandTapped="{Binding Source={x:Reference LineScroll}, Path=BindingContext.CommandOpenHGallery}"
                                            CornerRadius="12"
                                            HorizontalOptions="Fill"
                                            UseCache="ImageDoubleBuffered"
                                            VerticalOptions="Fill">
                                            <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">
                                                <!--  image will be parallaxed, we cache and then offset the cache with TranslationX  -->
                                                <!--  cache is bigger than surrounding shape because of negative margins  -->
                                                <draw:SkiaImage
                                                    Margin="-16"
                                                    Aspect="AspectCover"
                                                    EraseChangedContent="True"
                                                    HorizontalOptions="Fill"
                                                    LoadSourceOnFirstDraw="False"
                                                    Tag="ImageBanner"
                                                    VerticalOptions="Fill"
                                                    ZIndex="-1" />

                                                <!--  cached overlay data  -->
                                                <!--  using Image cache for gradient  -->
                                                <draw:SkiaLayout
                                                    HorizontalOptions="Fill"
                                                    UseCache="Image"
                                                    VerticalOptions="Fill">

                                                    <draw:SkiaShape HorizontalOptions="Fill" VerticalOptions="Fill">
                                                        <draw:SkiaShape.FillGradient>
                                                            <draw:SkiaGradient
                                                                EndXRatio="0"
                                                                EndYRatio="1"
                                                                StartXRatio="0"
                                                                StartYRatio="0"
                                                                Type="Linear">
                                                                <draw:SkiaGradient.Colors>
                                                                    <Color>#00000000</Color>
                                                                    <Color>#33000000</Color>
                                                                    <Color>#F0000000</Color>
                                                                </draw:SkiaGradient.Colors>
                                                                <draw:SkiaGradient.ColorPositions>
                                                                    <x:Double>0.4</x:Double>
                                                                    <x:Double>0.5</x:Double>
                                                                    <x:Double>1.0</x:Double>
                                                                </draw:SkiaGradient.ColorPositions>
                                                            </draw:SkiaGradient>
                                                        </draw:SkiaShape.FillGradient>
                                                    </draw:SkiaShape>

                                                    <!--  title  -->
                                                    <draw:SkiaLabel
                                                        Margin="8,0"
                                                        FontFamily="FontTextBold"
                                                        FontSize="20"
                                                        LineBreakMode="TailTruncation"
                                                        MaxLines="1"
                                                        Tag="LabelTitle"
                                                        Text="Title"
                                                        TextColor="White"
                                                        TranslationY="8"
                                                        VerticalOptions="Center" />

                                                    <!--  sub-title  -->
                                                    <draw:SkiaLabel
                                                        Margin="8,0"
                                                        FontSize="10"
                                                        LineBreakMode="TailTruncation"
                                                        LineSpacing="-2"
                                                        MaxLines="3"
                                                        Opacity="0.66"
                                                        Tag="LabelDesc"
                                                        Text="Description"
                                                        TextColor="White"
                                                        TranslationY="92"
                                                        UseCache="Operations"
                                                        VerticalOptions="Start" />

                                                    <!--  id  -->
                                                    <!--<draw:SkiaLabel
                                                    Margin="8"
                                                    FontFamily="FontTextBold"
                                                    FontSize="30"
                                                    HorizontalOptions="End"
                                                    MaxLines="1"
                                                    Tag="LabelId"
                                                    Text="ID"
                                                    TextColor="#99ff0000"
                                                    VerticalOptions="Start" />-->

                                                </draw:SkiaLayout>
                                            </draw:SkiaLayout>
                                        </controls:SkiaShapeTouch>

                                    </controls:FastCellWithBanner>


                                </DataTemplate>
                            </draw:SkiaLayout.ItemTemplate>

                        </draw:SkiaLayout>

                        <!--  FOOTER  -->
                        <!--  using footer as padding here  -->
                        <draw:SkiaScroll.Footer>
                            <draw:SkiaControl VerticalOptions="Fill" WidthRequest="8" />
                        </draw:SkiaScroll.Footer>
                    </draw:SkiaScroll>

                </content:ScrollHeader>
            </draw:SkiaScroll.Header>

            <!--  FOOTER  -->
            <draw:SkiaScroll.Footer>

                <!--  using footer as content padding for bottom tabs AND for LoadMore placeholder  -->
                <draw:SkiaControl
                    BackgroundColor="Transparent"
                    AddMarginBottom="50"
                    HeightRequest="{Binding Presentation.BottomTabsHeightRequest}"
                    WidthRequest="1" />
            </draw:SkiaScroll.Footer>

            <!--  REFRESH VIEW  -->
            <draw:SkiaScroll.RefreshIndicator>
                <draw:LottieRefreshIndicator HeightRequest="100">

                    <draw:SkiaLottie
                        AutoPlay="False"
                        HorizontalOptions="Center"
                        LockRatio="1"
                        Repeat="-1"
                        Source="Lottie/Loader.json"
                        Tag="Loader"
                        VerticalOptions="Center"
                        WidthRequest="50">
                        <draw:SkiaLottie.Colors>
                            <Color>#FF0000</Color>
                            <Color>#FFFFFF</Color>
                        </draw:SkiaLottie.Colors>
                    </draw:SkiaLottie>
                </draw:LottieRefreshIndicator>
            </draw:SkiaScroll.RefreshIndicator>

            <!--  VERTICAL CELLS  -->
            <!--  RecyclingTemplate + UseCacheDoubleBuffering..  -->

            <content:AnimateVerticalStack
                x:Name="StackCells"
                DynamicColumns="True"
                HorizontalOptions="Fill"
                ItemsSource="{Binding Items}"
                Spacing="0"
                Split="{Binding Columns}"
                Tag="StackItemsV"
                Type="Column">

                <draw:SkiaLayout.ItemTemplate>
                    <DataTemplate>

                        <controls:FastCellWithBanner
                            draw:AddGestures.AnimationTapped="Ripple"
                            x:DataType="viewModels:SimpleItemViewModel"
                            HeightRequest="220"
                            HorizontalOptions="Fill"
                            IsClippedToBounds="True"
                            Tag="ListCell"
                            UseCache="ImageDoubleBuffered">

                            <!--  DRAWER  -->
                            <!--  cell content is inside drawer, controls will be behind  -->
                            <!--  to simulate Maui SwipeView  -->
                            <draw:SkiaDrawer
                                AmplitudeSize="60"
                                Direction="FromLeft"
                                HorizontalOptions="Fill"
                                IgnoreWrongDirection="True"
                                IsOpen="True"
                                Tag="Drawer"
                                VerticalOptions="Fill"
                                ZIndex="1">

                                <!--  usual cell content  -->
                                <draw:ContentLayout
                                    HorizontalOptions="Fill"
                                    UseCache="Image"
                                    VerticalOptions="Fill">

                                    <controls:SkiaShapeTouch
                                        Margin="8,2,8,14"
                                        CommandTapped="{Binding Source={x:Reference StackCells}, Path=BindingContext.CommandOpenGallery}"
                                        CornerRadius="12,40,12,12"
                                        HorizontalOptions="Fill"
                                        Tag="DrawerContent"
                                        VerticalOptions="Fill">

                                        <draw:SkiaShape.Shadows>

                                            <draw:SkiaShadow
                                                Blur="3"
                                                Opacity="0.25"
                                                X="3"
                                                Y="3"
                                                Color="Black" />

                                        </draw:SkiaShape.Shadows>

                                        <draw:SkiaControl.FillGradient>

                                            <draw:SkiaGradient
                                                EndXRatio="1"
                                                EndYRatio="1"
                                                StartXRatio="0"
                                                StartYRatio="0"
                                                Type="Linear">
                                                <draw:SkiaGradient.Colors>
                                                    <Color>#444444</Color>
                                                    <Color>#888888</Color>
                                                </draw:SkiaGradient.Colors>
                                            </draw:SkiaGradient>

                                        </draw:SkiaControl.FillGradient>

                                        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">
                                            <!--  FastCellWithBanner will set LoadSourceOnFirstDraw to True after the first context is set  -->
                                            <!--  this way we dont have empty cells at start but then we do not slow the scrolling  -->
                                            <draw:SkiaImage
                                                UseCache="Image"
                                                Aspect="AspectCover"
                                                EraseChangedContent="True"
                                                HorizontalOptions="Fill"
                                                LoadSourceOnFirstDraw="False"
                                                Tag="ImageBanner"
                                                VerticalOptions="Fill"
                                                ZIndex="-1" />

                                            <!--  gradient overlay  -->
                                            <draw:SkiaShape
                                                HorizontalOptions="Fill"
                                                Tag="Shadow"
                                                UseCache="Operations"
                                                VerticalOptions="Fill">
                                                <draw:SkiaShape.FillGradient>
                                                    <draw:SkiaGradient
                                                        EndXRatio="0"
                                                        EndYRatio="1"
                                                        StartXRatio="0"
                                                        StartYRatio="0"
                                                        Type="Linear">
                                                        <draw:SkiaGradient.Colors>
                                                            <Color>#00000000</Color>
                                                            <Color>#33000000</Color>
                                                            <Color>#F0000000</Color>
                                                        </draw:SkiaGradient.Colors>
                                                        <draw:SkiaGradient.ColorPositions>
                                                            <x:Double>0.33</x:Double>
                                                            <x:Double>0.45</x:Double>
                                                            <x:Double>1.0</x:Double>
                                                        </draw:SkiaGradient.ColorPositions>
                                                    </draw:SkiaGradient>
                                                </draw:SkiaShape.FillGradient>
                                            </draw:SkiaShape>

                                            <!--  title  -->
                                            <draw:SkiaLabel
                                                Margin="16,0,16,50"
                                                FontFamily="FontTextBold"
                                                FontSize="24"
                                                LineBreakMode="TailTruncation"
                                                MaxLines="1"
                                                Tag="LabelTitle"
                                                Text="Title"
                                                TextColor="White"
                                                UseCache="Operations"
                                                VerticalOptions="End" />

                                            <!--  sub-title  -->
                                            <draw:SkiaLabel
                                                Margin="16,164,16,0"
                                                FontFamily="FontText"
                                                FontSize="14"
                                                LineBreakMode="TailTruncation"
                                                LineSpacing="-2"
                                                MaxLines="2"
                                                Opacity="0.66"
                                                Tag="LabelDesc"
                                                Text="Description"
                                                TextColor="White"
                                                UseCache="Operations"
                                                VerticalOptions="Start" />

                                            <!--  NUMBER  -->
                                            <draw:SkiaLabel
                                                Margin="0,16,24,0"
                                                FontFamily="FontTextBold"
                                                FontSize="30"
                                                HorizontalOptions="End"
                                                MaxLines="1"
                                                Tag="LabelId"
                                                Text="ID"
                                                TextColor="#66ff0000"
                                                UseCache="Operations" />
                                        </draw:SkiaLayout>
                                    </controls:SkiaShapeTouch>

                                </draw:ContentLayout>

                            </draw:SkiaDrawer>

                            <!--  cell controls behind using Z order  -->
                            <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

                                <draw:SkiaShape
                                    Margin="0,0,12,0"
                                    BackgroundColor="Black"
                                    CornerRadius="6"
                                    HeightRequest="40"
                                    HorizontalOptions="End"
                                    LockRatio="1"
                                    UseCache="Operations"
                                    VerticalOptions="Center">

                                    <draw:SkiaLabel
                                        FontSize="14"
                                        HorizontalOptions="Center"
                                        Text="X"
                                        TextColor="White"
                                        VerticalOptions="Center" />

                                </draw:SkiaShape>

                            </draw:SkiaLayout>

                        </controls:FastCellWithBanner>

                    </DataTemplate>
                </draw:SkiaLayout.ItemTemplate>

                <!--  EMPTY  -->
                <draw:SkiaLayout.EmptyView>

                    <draw:SkiaLayout HeightRequest="150" HorizontalOptions="Fill">

                        <draw:SkiaLottie
                            AutoPlay="True"
                            HorizontalOptions="Center"
                            LockRatio="1"
                            Repeat="-1"
                            Source="Lottie/Loader.json"
                            Tag="EmptyIndicator"
                            VerticalOptions="Center"
                            WidthRequest="50">
                            <draw:SkiaLottie.Colors>
                                <Color>#FF0000</Color>
                                <Color>#FFFFFF</Color>
                            </draw:SkiaLottie.Colors>
                        </draw:SkiaLottie>

                    </draw:SkiaLayout>

                </draw:SkiaLayout.EmptyView>

            </content:AnimateVerticalStack>


        </draw:SkiaScroll>

    </draw:SkiaLayout>

    <draw:SkiaLabel
        Margin="8"
        AddMarginBottom="140"
        BackgroundColor="Black"
        HorizontalOptions="Start"
        InputTransparent="True"
        IsVisible="{Binding IsDebug}"
        Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
        TextColor="LawnGreen"
        VerticalOptions="End"
        ZIndex="100" />

</controls:TabCollectionViewDemo>


