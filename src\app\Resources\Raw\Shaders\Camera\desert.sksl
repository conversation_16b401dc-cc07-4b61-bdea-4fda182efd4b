﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;          // Texture
uniform float2 iOffset;          // Top-left corner of DrawingRect
uniform float2 iOrigin;          // Mouse drag started here

/// <summary>
/// Applies the gritty desert apocalypse look from Mad Max Fury Road
/// </summary>
/// <param name="color">Input RGB color</param>
/// <returns>Mad Max styled color</returns>
half3 madMaxGrade(half3 color) {
    float luminance = dot(color, half3(0.299, 0.587, 0.114));
    
    // Heavy desaturation in highlights for that bleached look
    float highlightMask = smoothstep(0.7, 1.0, luminance);
    half3 desaturatedHighlights = mix(color, half3(luminance), highlightMask * 0.7);
    
    // Warm orange cast overall
    half3 warmColor = desaturatedHighlights;
    warmColor.r *= 1.25;
    warmColor.g *= 1.05;
    warmColor.b *= 0.75;
    
    // Crush blacks slightly for that gritty look
    warmColor = max(warmColor - 0.05, 0.0) * 1.05;
    
    // Boost contrast in midtones
    float midtoneMask = smoothstep(0.2, 0.8, luminance) * (1.0 - highlightMask);
    warmColor = mix(warmColor, (warmColor - 0.5) * 1.3 + 0.5, midtoneMask);
    
    // Add slight yellow shift for desert heat
    warmColor.r *= 1.1;
    warmColor.g *= 1.08;
    
    // Reduce overall saturation for realism
    float finalLuminance = dot(warmColor, half3(0.299, 0.587, 0.114));
    half3 finalColor = mix(half3(finalLuminance), warmColor, 0.85);
    
    return clamp(finalColor, 0.0, 1.0);
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 gradedColor = madMaxGrade(originalColor.rgb);
    
    return half4(gradedColor, originalColor.a);
}