﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaShell
    x:Class="AppoMobi.Maui.DrawnUi.Demo.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:MainPageViewModel">

    <draw:Canvas
        x:Name="Canvas"
        Gestures="Enabled"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"
        VerticalOptions="Fill"
        WillFirstTimeDraw="Canvas_WillFirstTimeDraw">

        <!--  ShellLayout  -->
        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

            <!--  FPS  -->
            <draw:SkiaLabelFps
                Margin="0,0,4,84"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End" />

            <!--  required root layout wil be replaced by skiashell  -->
            <draw:SkiaControl Tag="RootLayout" />

            <!--  MENU  -->
            <!--<draw:SkiaLayout
                Padding="0"
                BackgroundColor="CadetBlue"
                HorizontalOptions="End"
                Type="Column"
                VerticalOptions="Fill"
                WidthRequest="140"
                ZIndex="-1">

                <draw:SkiaLabel
                    HorizontalOptions="Center"
                    Text="MENU"
                    TextColor="White" />

            -->
            <!--<draw:SkiaLabel
                    HorizontalOptions="Center"
                    IsVisible="False"
                    Text="OPTION"
                    TextColor="White" />-->
            <!--


            </draw:SkiaLayout>

            <draw:SkiaShape
                BackgroundColor="Red"
                HorizontalOptions="End"
                LockRatio="1"
                VerticalOptions="End"
                WidthRequest="140"
                ZIndex="100" />-->

        </draw:SkiaLayout>

    </draw:Canvas>

</draw:SkiaShell>
